{"description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "setting": {"urlCheck": true, "es6": false, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "ignoreUploadUnusedFiles": true, "useStaticServer": true, "condition": false}, "compileType": "miniprogram", "libVersion": "2.21.1", "appid": "wx47f3b3a5ebdff321", "projectname": "%E5%A5%88%E7%89%B9%E7%91%9E%E6%A0%87%E7%89%88", "condition": {"miniprogram": {"list": [{"name": "pages/auth/login/login", "pathName": "pages/auth/login/login", "query": "", "scene": null}, {"name": "pages/videoInquiry/videoInquiry", "pathName": "pages/videoInquiry/videoInquiry", "query": "", "scene": 1047}, {"name": "pages/meeting/meeting", "pathName": "pages/meeting/meeting", "query": "", "scene": null}]}}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}}