// pages/people/people.js
var api = require('../../../config/api.js')
const util = require('../../../utils/util')
import Dialog from '../../../lib/vant-weapp/dialog/dialog'
const app = getApp()
Page({

    /**
     * 页面的初始数据
     */
    data: {
        type: null, //1. 图文问诊  2.视频问诊,
        source: null, //来源：1.医生详情 2.个人中心
        list: [],
        imgObject: {
            ic_address_edit: api.ImgUrl + 'images/ic_address_edit.png',
            ic_address_deleted: api.ImgUrl + 'images/ic_address_deleted.png',
            nomes: api.ImgUrl + 'images/nomes.png'
        },
        isBack: true,
        backgroundColor: '#fff',
        navTitle: '选择就诊人',
        selfInfo: {
            name: '',
            idCard: '',
            phone: ''
        },
        from: "",
    },
    //添加人员方法
    addPeople: function (e) {
        const idcard = e.currentTarget.dataset.idcard
        const phone = e.currentTarget.dataset.phone
        const name = e.currentTarget.dataset.name
        if (this.data.list.length >= 8) {
            util.showToast({title: '最多添加8人'})
            return false
        } else {
            wx.navigateTo({
                url: '/pages/peopleContent/addPeople/addPeople?type=' + this.data.type + '&doctorId=' + this.data.doctorId + '&source=' + this.data.source + '&name=' + name + '&phone=' + phone + '&idcard=' + idcard
            })
        }

    },
    // 人员详情
    peopleDetail: function (e) {
        const model = e.currentTarget.dataset.model//成人还是儿童
        const index = e.currentTarget.dataset.index
        const id = this.data.list[index].inquirerId
        wx.navigateTo({
            url: '/pages/peopleContent/detail/detail?model=' + model + '&inquirerId=' + id
        })
    },
    // 删除
    del(e) {
        var index = e.currentTarget.dataset.index
        var id = this.data.list[index].inquirerId
        var that = this
        util.showModal({
            content: '就诊人删除后不可恢复，历史就诊记录还会保留，您确认删除吗？',
            showCancel: true,
            cancelText: '取消',
            cancelColor: '#666666',
            confirmText: '确认',
            success: function (res) {
                if (!res.cancel) {
                    util.request(api.delPeople, {inquirerId: id}, 'post', 2)
                        .then(res => {
                            if (res.data.code === 0) {
                                util.showToast({
                                    title: '删除成功',
                                    icon: 'success',
                                    duration: 1000
                                })
                                setTimeout(() => {
                                    that.getList()
                                }, 1000)

                            } else {
                                util.showToast({'title': res.data.msg})
                            }
                        })
                        .catch(res => {
                            console.log(res)
                        })
                }
            }
        })
    },
    // 选择人员
    async choosePeople(e) {
        console.log(this.data.from)
        // const type = this.data.type
        // const doctorId = this.data.doctorId
        const inquirerId = e.currentTarget.dataset.id
        const index = e.currentTarget.dataset.index
        if (this.data.source === 1) {
            if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                return false
            }
            var pages = getCurrentPages()
            var prevPage = pages[pages.length - 2]
            prevPage.setData({
                ['info.inquirerId']: inquirerId,
                isReset: true
            })
            wx.navigateBack({
                delta: 1
            })
        } else {
            if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                return false
            }
            console.log("this.data.list[index] = ", this.data.list[index])
            let {inquirerId, name, birthday} = this.data.list[index]
            const { data } = await util.request(api.getYgConfig, { inquirerId }, 'get')
            if (data.code !== 0) {
                util.showToast({title: '获取元罡配置信息失败'})
                return
            }
            const { memberId, partnerId, url } = data.data
            // 首页-健康检测-勃起评测(调用半屏小程序)
            // 参数	            示例	        必填	描述
            // partnerId	    aiDing	        是	    固定 合作平台唯一标识
            // memberId	        *************	是	    你们用户唯一标识id(不超过20字符)
            // name	            啊龙	        否	    姓名
            // birthday	        2022-02-01	    否	    出生日期
            if (this.data.from == "EHS") {
                // const miniProgramPath = `pages/index/index?partnerId=${partnerId}&memberId=${WxApiRoot.includes('https://api-adys.moonhealth.cn') ? 'prod_' : 'dev_'}${memberId}&name=${name}&birthday=${birthday}`
                const miniProgramPath = `pages/index/index?partnerId=${partnerId}&memberId=${memberId}&name=${name}&birthday=${birthday}&isEncrypt=1`

                util.openHalfScreenMiniProgram(
                    'wx2a7f7248cb60f2d6',
                    miniProgramPath,
                    {},
                    (res) => {
                        console.log("调用半屏小程序成功！")
                    },
                    (err) => {
                        console.log("调用半屏小程序失败 err = ", err)
                    }
                )
            }
            // 首页-报告查询-勃起评测报告
            else if (this.data.from == "EHS_REPORT") {
                // const env = WxApiRoot.includes('https://api-adys.moonhealth.cn') ? 'prod_' : 'dev_'
                // const reportUrl = `${url}?memberId=${env}${memberId}&partnerId=${partnerId}`
                const reportUrl = `${url}?memberId=${memberId}&partnerId=${partnerId}&isEncrypt=1`
                console.log(reportUrl, 'reportUrl')
                const encodedUrl = encodeURIComponent(reportUrl)

                wx.navigateTo({
                    url: `/pages/webView/index?url=${encodedUrl}`
                })
            }

            // 美高购买 或者 健康档案
            else if (this.data.from == "MEIGAO" || this.data.from === 'HEALTH_RECORDS' || this.data.from === 'MEIGAO_DETAIL' || this.data.from === 'SPORT') {
                if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                    util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                    return false
                }
                var pages = getCurrentPages()
                var prevPage = pages[pages.length - 2]
                prevPage.setData({
                    ['inquirerId']: inquirerId,
                    ['rangeCode']: Math.random(),
                })
                wx.navigateBack()
            }

            else if (this.data.from === 'DIET_MANAGEMENT' ){
                if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                    util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                    return false
                }
                wx.redirectTo({
                    url:'/pages/dietManagement/index?inquirerId='+inquirerId
                })
            }

            else if (this.data.from === 'DIET_MANAGEMENT_SPORT' ){
                if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                    util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                    return false
                }
                wx.redirectTo({
                    url:'/pages/dietManagement/index?type=3&inquirerId='+inquirerId
                })
            }


            // AI运动
            else if (this.data.from == "AI_SPORT") {
                console.log(12321321)
                if (this.data.list[index].childTag === 0 && (this.data.list[index].idCard === '' || this.data.list[index].idCard === null)) {
                    util.showToast({title: '当前就诊人信息未实名，请先进行实名认证！'})
                    return false
                }
                // var pages = getCurrentPages()
                // var prevPage = pages[pages.length - 2]
                // prevPage.setData({
                //     ['inquirerId']: inquirerId,
                //     ['rangeCode']: Math.random(),
                // })
                // wx.navigateBack()
                util.request(api.sportInfo,{inquirerId},'GET').then(res=>{
                    console.log(res.data)
                    if (res.data.code === 0){
                        wx.navigateToMiniProgram({
                            appId: 'wxa3a88c6a423e6931',
                            extraData: {
                                token: res.data.data.token
                            },
                            envVersion: 'release',
                            success(res) {
                                // 打开成功
                            }
                        })
                    }else {
                        Dialog.alert({
                            message: res.data.msg
                        })
                    }
                })
            }
            return false
        }
    },

    // 就诊人列表
    async getList() {
        util.showToast({
            title: '加载中..',
            icon: 'loading'
        })
        var data = await util.getPeopleList()
        util.hideLoading()
        data.forEach((element, index) => {
            if (element.relation === 0) {
                this.setData({
                    ['selfInfo.name']: element.name ? element.name : '',
                    ['selfInfo.idCard']: element.idCard ? element.idCard : '',
                    ['selfInfo.phone']: element.phone ? element.phone : ''
                })
            }
            element.guardianIdCard = util.stringHidden(element.guardianIdCard, 6, 4)
            element.guardianPhone = util.stringHidden(element.guardianPhone, 3, 4)
            element.idCard = util.stringHidden(element.idCard, 6, 4)
            element.phone = util.stringHidden(element.phone, 3, 4)
        })

        this.setData({
            list: data
            // list: [{idCard:null,idCard:null,childTag:0,relationName:'本人',name: "五"}]
        })
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        console.log("from = ", options.from)
        this.setData({
            type: options.type * 1,
            source: options.source * 1,
            doctorId: options.doctorId,
            navTitle: options.source * 1 === 1 ? '选择就诊人' : '就诊人管理',
            from: options.from
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {

    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        this.getList()
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {

    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {

    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {

    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {

    }

    /**
     * 用户点击右上角分享
     */
    // onShareAppMessage: function () {

    // }
})
