<view class="home-page bg-color-f8">
	<view class="bg-header"></view>
	<view class="z-index-99" style="height:{{navBarHeight - 2}}px;">
		<view style="height: {{statusBarHeight}}px"></view>
		<image class="logo" src="{{imgObject.logo}}" mode="widthFix" />
	</view>
	<view class="search-area m24" bind:tap="goSearch">
		<view class="search-box">
			<image src="{{imgObject.ic_home_search}}" mode="widthFix" />
			<input class="search-input" type="text" disabled placeholder-style="color:#B4B4B4;font-size:28rpx;" placeholder="搜索疾病"  />
		</view>
	</view>
	<view class="agency">
		<image class="authentication" src="{{imgObject.agency}}" mode="widthFix" />
		<view class="agency-text">国家卫健委认证医疗机构</view>
	</view>
	<view class="bg-white-border-radius-20 m24 z-index-99">
		<view class="flex m70 justify-between">
			<view class="flex-item" wx:for="{{mainList}}" wx:key="index" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="toPage">
				<image class="icon" src="{{item.icon}}" mode="widthFix" />
				<view class="text">{{item.name}}</view>
			</view>
		</view>
		<view class="flex justify-between m28">
			<view class="flex-item" wx:for="{{subList}}" wx:key="index" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="toPage">
				<image class="icon-56" src="{{item.icon}}" mode="widthFix" />
				<view class="text-26">{{item.name}}</view>
			</view>
		</view>
	</view>
	<view wx:if="{{noticeList.length > 0}}" class="affair" bindtap="toNoticeListPage">
		<view class="flex fl_align">
			<image src="{{imgObject.ic_font_message}}" style="width:52rpx;height:52rpx" />
			<swiper autoplay circular vertical class="info-swiper">
				<block wx:for="{{noticeList}}" wx:key="index">
					<swiper-item>
						<view class="c333 f28 bl1 ml20 pl20 h100" data-id="{{item.id}}" catchtap="toNoticeDetail">{{item.title}}</view>
					</swiper-item>
				</block>
			</swiper>
		</view>
		<image style="width:32rpx" src="{{imgObject.ic_more_grey}}" mode="widthFix" />
	</view>
	<view class="banner">
		<swiper indicator-dots="{{true}}" indicator-type="expand" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}"
			indicator-color="{{indicatorColor}}" indicator-active-color="{{indicatorActiveColor}}" circular="{{circular}}"
			bindchange="swiperChange">
			<block wx:for="{{bannerList}}" wx:key="index" >
				<swiper-item data-type="{{item.bannerType}}" data-id="{{item.id}}" data-url="{{item.targetUrl}}" data-content="{{item.content}}" data-video="{{item.videoId}}" catch:tap="handleDetail">
					<image src="{{item.bannerUrl}}" mode="widthFix" class="imgBlock" style="border-radius: 20rpx" />
				</swiper-item>
			</block>
		</swiper>
	</view>
	<view class="bg-white-border-radius-20 p20" style="margin-top: 0;">
		<view class="c333 f34 font-weight-600 title1" style="line-height: 1.5">全病程管理数字化解决方案</view>
		<view class="c333 f28 font-weight-500 title2">— 让医学回归人本</view>
		<view class="flex flex-warp justify-between">
			<view class="item" style="background-image: url({{item.bgImage}})" wx:for="{{healthList}}" wx:key="key" data-type="{{item.type}}" data-url="{{item.url}}" bindtap="toPage">
				<view class="flex justify-between item-text">
					<view class="c333 f28 font-weight-600 mb6">{{item.name}}</view>
					<view class="c888 f24">{{item.subName}}</view>
				</view>
			</view>
		</view>
	</view>
</view>