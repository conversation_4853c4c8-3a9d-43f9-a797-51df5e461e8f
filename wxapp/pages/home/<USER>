.home-page {
  position: relative;
  height: auto;
  display: flex;
  flex-direction: column;
}

.bg-color-f8 {
  background-color: #f8f8f8;
}

.z-index-99 {
  z-index: 99;
}

.bg-header {
  position: absolute;
  width: 100%;
  height: 628rpx;
  background: url('https://patient-adys.moonhealth.cn/images/adys/img_home_bg.png') no-repeat;
  background-size: 100% 100%;
}

.logo {
  width: 150rpx;
  height: 80rpx;
  margin-left: 54rpx;
}

.m24 {
  margin: 0 24rpx;
}

.m70 {
  margin: 40rpx 70rpx;
}

.m28 {
  margin: 0 28rpx 40rpx;
}

.c333 {
  color: #333333;
}

.mb6 {
  margin-bottom: 6rpx;
}

.f34 {
  font-size: 34rpx;
}

.f24 {
  font-size: 24rpx;
}

.c888 {
  color: #888888;
}

.f28 {
  font-size: 28rpx;
}

.mr20 {
  margin-right: 20rpx;
}

.font-weight-600 {
  font-weight: 600;
}

.font-weight-500 {
  font-weight: 500;
}

.icon-56 {
  width: 56rpx;
  height: 56rpx;
}

.justify-between {
  justify-content: space-between;
}

.search-area {
  margin-top: 24rpx;
  height: 72rpx;
  background: #ffffff;
  border-radius: 36rpx;
  z-index: 99;
  display: flex;
  align-items: center;
}

.search-box {
  display: flex;
  align-items: center;
  width: 100%;
}

.search-box image {
  width: 40rpx;
  height: 40rpx;
  margin-left: 20rpx;
  margin-right: 14rpx;
}

.search-input {
  flex: 1;
  font-weight: 400;
  font-size: 28rpx;
  /* color: #b4b4b4; */
  background: transparent;
}

.agency {
  display: flex;
  align-items: center;
  z-index: 99;
  margin: 12rpx 0 12rpx 44rpx;
}

.authentication {
  width: 24rpx;
  height: 28rpx;
}

.agency-text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  margin-left: 8rpx;
}

.bg-white-border-radius-20 {
  background: #ffffff;
  border-radius: 20rpx;
}

.flex-item {
  text-align: center;
}

.flex-item .icon {
  width: 80rpx;
  height: 80rpx;
}

.flex-item .text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 30rpx;
  color: #333333;
  line-height: 21rpx;
  margin-top: 16rpx;
}

.text-26 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 26rpx;
  color: #666666;
  line-height: 18rpx;
  margin-top: 16rpx;
}

.affair {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20rpx 20rpx 0;
  padding: 14rpx 20rpx;
  background-color: #F2F6FF;
  border-radius: 8rpx;
}

.info-swiper {
  width: 350rpx;
  height: 40rpx;
}

.fl_align {
  align-items: center;
}

.banner {
  margin: 20rpx auto;
  width: 710rpx;
  height: 240rpx;
}

.p20 {
  margin: 20rpx;
}

.title2 {
  margin-bottom: 20rpx;
}

.item {
  width: calc((100% - 22rpx) / 2);
  height: 128rpx;
  background: #ffffff;
  border-radius: 16rpx;
  border: 2rpx solid #F8F8F8;
  display: flex;
  align-items: center;
  padding: 34rpx 20rpx;
  margin-bottom: 20rpx;
  background-size: cover;
  background-repeat: no-repeat;
}

.item:nth-child(9) {
  margin-bottom: 0;
}

.item:nth-child(10) {
  margin-bottom: 0;
}

.flex-warp {
  flex-wrap: wrap;
}

.item-text {
  flex-direction: column;
}

.icon-60 {
  width: 60rpx;
  height: 60rpx;
}

swiper {
  height: 240rpx;
}


