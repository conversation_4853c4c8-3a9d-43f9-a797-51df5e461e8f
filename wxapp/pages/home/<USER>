var api = require('../../config/api.js')
var util = require('../../utils/util')
var Config = require('../../config/index.js')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isBack: false,
    imgObject: {
      backgroundImgUrl: api.ImgUrl + 'images/adys/img_home_bg.png',
      logo: api.ImgUrl + 'images/adys/logo.png',
      ic_home_search: api.ImgUrl + 'images/adys/ic_home_search.png',
      agency: api.ImgUrl + 'images/adys/ic_authentication.png',
      ic_font_message: api.ImgUrl + 'images/adys/ic_font_message.png',
      ic_more_grey: api.ImgUrl + 'images/adys/ic_more_grey.png',
    },
    indicatorDots: false,
    autoplay: true,
    interval: 3000,
    duration: 500, //滑动动画时长
    circular: true,
    ndicatorColor: 'rgba(255,255,255,1)', //普通轮播点背景色
    indicatorActiveColor: '#2e9cff', //选中轮播点背景色
    swiperCurrent: 0,
    navBarHeight: app.globalData.navBarHeight,
    statusBarHeight: app.globalData.statusBarHeight,
    mainList: [ // type 1普通跳转 2 跳转tabbar
      {
        name: '就诊咨询',
        icon: api.ImgUrl + 'images/adys/ic_pre_consultation.png',
        url: '/pages/preconsultation/index'
      },
      {
        name: '我的医生',
        icon: api.ImgUrl + 'images/adys/ic_my_doctor.png',
        url: '/pages/myDoctor/index'
      },
      {
        name: '健康药房',
        icon: api.ImgUrl + 'images/adys/ic_health_pharmacy.png',
        url: '/pages/shop/shop',
        type: 1
      }
     ],
     subList:[
      {
        name: '量表测评',
        icon: api.ImgUrl + 'images/adys/ic_scale_evaluation.png',
        url: '/pages/follow/scaleEvaluation/index'
      },
      {
        name: '体检检验',
        icon: api.ImgUrl + 'images/adys/ic_physical_examination.png',
        url: '/pages/instrumentGoodList/index?id=1&title=体检检验'
      },
      {
        name: '报告查询',
        icon: api.ImgUrl + 'images/adys/ic_report_inquiry.png',
        url: '/pages/myOrder/index'
      },
      {
        name: '诊后随访',
        icon: api.ImgUrl + 'images/adys/ic_after_diagnosis.png',
        url: '/pages/follow/calendar/index'
      },
      {
        name: '病历档案',
        icon: api.ImgUrl + 'images/adys/ic_medical_records.png',
        url: '/pages/caseList/index'
      }
     ],
     healthList: [
      {
        name: '健康档案',
        subName: '健康记录 完备档案',
        icon: api.ImgUrl + 'images/adys/ic_health_records.png',
        url: '/pages/healthRecords/index',
        // type: 2,
        bgImage: api.ImgUrl + 'images/adys/bg_health_records.png'
      },
      {
        name: '健康监测',
        subName: '健康追踪 精准医疗',
        icon: api.ImgUrl + 'images/adys/ic_health_monitoring.png',
        url: '/pages/healthMonitoring/healthMonitoring',
        bgImage: api.ImgUrl + 'images/adys/bg_health_monitoring.png'
      },
      {
        name: '饮食管理',
        subName: '营养均衡 科学安排',
        icon: api.ImgUrl + 'images/adys/ic_diet_management.png',
        url: '/pages/dietManagement/index',
        bgImage: api.ImgUrl + 'images/adys/bg_diet_management.png'
      },
      {
        name: '运动管理',
        subName: '运动轨迹 细致留存',
        icon: api.ImgUrl + 'images/adys/ic_sport_management.png',
        url: '/pages/dietManagement/index?type=3',
        bgImage: api.ImgUrl + 'images/adys/bg_sport_management.png'
      },
      {
        name: 'AI运动处方',
        subName: '智享运动 科学健身',
        icon: api.ImgUrl + 'images/adys/ic_ai_prescription.png',
        url: '/pages/aiSport/index',
        bgImage: api.ImgUrl + 'images/adys/bg_ai_prescription.png'
      },
      {
        name: '失眠数字疗法',
        subName: '数字助眠 科学调理',
        icon: api.ImgUrl + 'images/adys/ic_sleep_dtx.png',
        url: '/pages/mgDtx/index?type=sm',
        bgImage: api.ImgUrl + 'images/adys/bg_sleep_dtx.png'
      },
      {
        name: '情绪数字疗法',
        subName: '数字疗愈 情绪舒缓',
        icon: api.ImgUrl + 'images/adys/ic_emotion_dtx.png',
        bgImage: api.ImgUrl + 'images/adys/bg_emotion_dtx.png',
        url: '/pages/mgDtxQx/index'
      },
      {
        name: '压力数字疗法',
        subName: '数字调节 压力无忧',
        icon: api.ImgUrl + 'images/adys/ic_pressure_dtx.png',
        bgImage: api.ImgUrl + 'images/adys/bg_pressure_dtx.png',
        url: '/pages/mgDtx/index?type=yl'
      },
      {
        name: '戒烟限酒',
        subName: '科学戒烟 理智限酒',
        icon: api.ImgUrl + 'images/adys/ic_tobacco_alcohol.png',
        url: '/pages/instrumentGoodList/index?title=戒烟限酒&id=2',
        bgImage: api.ImgUrl + 'images/adys/bg_tobacco_alcohol.png'
      },
      {
        name: '幸福学院',
        subName: '健康之路 幸福相伴',
        icon: api.ImgUrl + 'images/adys/ic_college.png',
        url: '/pages/happyAcademy/index',
        bgImage: api.ImgUrl + 'images/adys/bg_college.png'
      }
     ],
     noticeList: []
  },

  async getList() {
    try {
      const { data } = await util.request(api.noticeList, {}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        noticeList: data.data.result
      })
    } catch (error) {
      throw new Error(error)
    }
  },

  async getBannerList() {
    try {
      const { data } = await util.request(api.bannerList, {}, 'get', 1, false)
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        bannerList: data.data,
        indicatorDots: data.data.length > 1 ? true : false,
        autoplay: data.data.length > 1 ? true : false
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  // 跳转方法
  toPage(e) {
    const { url, type } = e.currentTarget.dataset
    if (type === 1) {
      wx.switchTab({ url })
    } else if (type === 2) {
      // 此处用于测试半屏小程序，可删除
      // util.openHalfScreenMiniProgram('wx2a7f7248cb60f2d6', 'pages/index/index', {token: 123}, (res) => {
      //   console.log('半屏小程序打开成功', res)
      // }, (err) => {
      //   console.error('半屏小程序打开失败', err)
      // })
    } else {
      wx.navigateTo({ url })
    }
  },

  // 院务列表页
  toNoticeListPage() {
    wx.navigateTo({ url: '/pages/noticeList/index' })
  },

  // 院务详情
  toNoticeDetail(event) {
    const { id } = event.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/noticeDetail/index?id=${id}`
    })
  },

  // 智能导诊
  goSearch() {
    wx.navigateTo({
      url: '/pages/guidance/index'
    })
  },

  handleDetail(e) {
    const { type, id, url, content, video } = e.currentTarget.dataset
    if (type === 1) {
      if (!content && !video) return
      wx.navigateTo({
        url: `/pages/article/articleDetail/index?id=${id}&type=banner`
      })
    } else if (type === 2) {
      if (!url) return
      wx.navigateTo({
        url: `/pages/webView/index?url=${url}`
      })
    } else {
      if (!url) return
      // 小程序内页面
      wx.navigateTo({
        url: url
      })
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    this.getList()
    this.getBannerList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {},
})
