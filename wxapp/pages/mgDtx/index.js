// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    type: '',
    rangeCode: '',
    inquirerId: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
     this.data.type = options.type
    // const url = api.WebViewUrl + `/dtxPayDone?orderId=${id}&token=` + wx.getStorageSync('token')
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login?params=' + encodeURIComponent(JSON.stringify({type:this.data.type}))
          })
        }else {
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    }else{
      this.loadPage()
    }
  },

  loadPage(){
    let url =api.WebViewUrl + `/#/dtxDetail?type=${this.data.type}&token=` + wx.getStorageSync('token')
    // let url ='https://localhost:8066' + `/#/dtxDetail?type=${this.data.type}&token=` + wx.getStorageSync('token')
    console.log(url)
    if (this.data.inquirerId){
      url+=`&inquirerId=${this.data.inquirerId}&rangeCode=${this.data.rangeCode}`
    }
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
