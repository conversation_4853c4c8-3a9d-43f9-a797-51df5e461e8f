const util = require('../../../utils/util')
const api = require('../../../config/api.js')

Page({
  data: {
    id: '',
    list: [],
    tabList: ['未开始', '进行中', '已结束'],
    listQuery: {
      page: 1,
      status: 1
    },
    static: {
      ic_task: api.ImgUrl + 'images/ic_task.png',
      ic_follow_time: api.ImgUrl + 'images/ic_follow_time.png',
      ic_follow_person: api.ImgUrl + 'images/ic_follow_person.png',
      ic_follow_form: api.ImgUrl + 'images/ic_follow_form.png',
      bg_follow_task: api.ImgUrl + 'images/bg_follow_task.png',
      ic_follow_doctor: api.ImgUrl + 'images/ic_follow_doctor.png'
    },
    currentNum: 0
  },
  onLoad(options) {
    this.setData({
      id: options.id || '',
      'listQuery.status': options.status || 1
    }, () => { })
  },
  onShow() {
    console.log(this.data.currentNum, 33)
    this.getList('onload')
  },
  async getList(params) {
    try {
      const {
        data
      } = await util.request(api.getFollowupList, this.data.listQuery, 'get')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }
      let {
        id, currentNum
      } = this.data
      const list = data.data
      if (params === 'onload' && id) {
        currentNum = list.findIndex((item) => {
          return id == item.id
        })
      }
      if (data.data.length) {
        this.setData({
          list: data.data,
          id: data.data[0].id,
          currentNum: currentNum
        })
      }
    } catch (error) {
      throw new Error(error)
    }
  },
  handleGoFrom(e) {
    const {
      id,
      formid
    } = e.currentTarget.dataset
    if (this.data.listQuery.status == 0) {
      util.showToast({
        title: '当前任务暂未开始'
      })
      return
    }
    this.data.id = id
    const { list = [] } = this.data
    const fromData = list.find(({ followUpRecordFormId }) => followUpRecordFormId == formid) || {}
    wx.navigateTo({
      url: `/pages/from/fromDetail/index?id=${id}&formId=${formid}&finishStatus=${fromData.fillingStatus}`
    })
  },
  handleSwitchTab(e) {
    const {
      status
    } = e.currentTarget.dataset
    this.setData({
      list: [],
      currentNum: 0,
      'listQuery.page': 1,
      'listQuery.status': status
    }, () => {
      this.getList()
    })
  },
  // 监听轮播
  onSwiperChange(e) {
    const {
      current
    } = e.detail
    this.setData({
      currentNum: current,
      id: this.data.list[current].id
    })
  }
})
