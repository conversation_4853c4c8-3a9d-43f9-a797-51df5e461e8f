<navbar isBack="{{isBack}}" isHome="{{true}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="login flex_line_c">
  <image class="logo" mode="widthFix" src="{{login_logo}}" />
  <view class="f28 c666 mt20">{{company}}</view>
  <view class="login-box">
    <button  class="wx-login-btn flex_c_m f32 tc themeBtn f-b" bindtap="getUserProfile" wx:if="{{canIUseGetUserProfile}}"><image class="wx"  src="/static/images/wx.png" />微信授权登录</button>
    <button open-type="getUserInfo" class="wx-login-btn flex_c_m f32 tc themeBtn f-b" bindgetuserinfo="wxLogin" wx:else><image class="wx" src="/static/images/wx.png" />微信授权登录</button>
    <view class="no-login" bindtap="noLogin">暂不登录</view>
  </view>
</view>
