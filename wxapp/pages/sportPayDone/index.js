// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
Page({

    /**
     * 页面的初始数据
     */
    data: {
        orderId: '',
        counselorQrCodeUrl:'',
        inquirerId:'',
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.data.orderId = options.orderId
        util.request(`${api.sportOrderDetail}${this.data.orderId}`,{},'GET').then(res=>{
            this.data.counselorQrCodeUrl = res.data.data.counselorQrCodeUrl
            this.data.inquirerId = res.data.data.inquirerId
            this.setData({
                counselorQrCodeUrl:this.data.counselorQrCodeUrl
            })
        })
    },

    toOrder() {
        wx.navigateTo({
            url: '/pages/sportOrderDetail/index?isDetail=true&orderId=' + this.data.orderId
        })
    },
    buildQueryString(params) {
        const pairs = [];
        for (const [key, value] of Object.entries(params)) {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
        return pairs.join('&');
    },

    toSport() {
        util.request(api.sportInfo,{inquirerId:this.data.inquirerId},'GET').then(res=>{
            console.log(res.data)
            wx.navigateToMiniProgram({
                appId: 'wxa3a88c6a423e6931',
                extraData: {
                    token: res.data.data.token
                },
                envVersion: 'release',
                success(res) {
                    // 打开成功
                }
            })
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})
