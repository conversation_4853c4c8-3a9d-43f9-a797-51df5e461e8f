<navbar backgroundColor='#fff' navTitle="AI运动处方订单"></navbar>
<van-tabs active="{{ active }}" color="#367DFF" title-active-color="#1750DC" line-width="90" sticky
          bind:change="onChange">
    <van-tab title="全部"></van-tab>
    <van-tab title="待支付"></van-tab>
    <van-tab title="已支付"></van-tab>
</van-tabs>
<scroll-view bindscrolltolower="onScrollToLower" scroll-y="true" class="list" wx:if='{{list.length > 0}}'>
    <view class="list-items_1-0" wx:for="{{list}}" wx:key="id" data-id="{{item.id}}"
          bind:tap="goToCurrentQuePage">
        <text lines="1" class="text_6-0">{{item.inquirerName}}</text>
        <view class="image-text_1-0">
            <image src="{{item.imgPath}}"
                   class="image_4-0"></image>
            <view class="text-group_1-0">
                <text lines="1" class="text_7-0">{{item.prescriptionName}}</text>
                <text lines="1" class="text_8-0">¥{{item.amount}}</text>
            </view>
        </view>
        <view class="group_2-0">
            <text lines="1" class="text_9-0">有效期至:{{item.validTime || '--'}}</text>
            <view style="display: flex">
                <view class="text-wrapper_2-0" wx-if="{{item.payStatus === 1}}">
                    <text lines="1" class="text_10-0" data-id="{{item.inquirerId}}" catch:tap="toSport">去运动</text>
                </view>
                <view class="text-wrapper_2-0" wx-if="{{item.payStatus === 0}}" >
                    <text lines="1" class="text_10-0" data-id="{{item.inquirerId}}" catch:tap="toPay">去支付</text>
                </view>
                <view class="text-wrapper_2-0" wx-if="{{item.payStatus === 2}}" >
                    <text lines="1" class="text_10-0" data-id="{{item.inquirerId}}" catch:tap="toBuy">重新购买</text>
                </view>
                <view class="text-wrapper_2-0" wx-if="{{item.payStatus === 3}}" >
                    <text lines="1" class="text_10-0" data-id="{{item.inquirerId}}" catch:tap="toBuy">重新购买</text>
                </view>
                <view class="text-wrapper_2-0" wx-if="{{item.payStatus === 9}}" >
                    <text lines="1" class="text_10-0" data-id="{{item.inquirerId}}" catch:tap="toBuy">重新购买</text>
                </view>
            </view>
        </view>
    </view>
</scroll-view>
<view class="flex_line_c flex1 empty_icon" wx:else>
    <view class="f28 c666">当前暂无订单</view>
</view>
<van-dialog id="van-dialog" confirm-button-color="#367DFF" />