// pages/sportOrderList/index.js
var api = require('../../config/api.js')
const util = require("../../utils/util");
import Dialog from '../../lib/vant-weapp/dialog/dialog'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: 0,
    params:{
      num: 10,
      page: 1,
    },
    list: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getList()
  },
  getList(){
    const param = {
     ...this.data.params
    }
      param.status = this.data.active
    util.request(api.sportOrderList,param,'GET').then(res=>{
      if (this.data.params.page === 1){
        this.data.list = res.data.data.result
      }else {
        this.data.list = this.data.list.concat(res.data.data.result)
      }
      this.setData({
        list:this.data.list
      })
    })
  },
  onScrollToLower(){
    console.log('xxxxxx')
    if (this.data.hasNext){
      this.data.params.page += 1
      this.getList()
    }
  },
  goToCurrentQuePage(e) {
    wx.navigateTo({
      url: `/pages/sportOrderDetail/index?orderId=${e.currentTarget.dataset.id}&isDetail=true`
    })
  },
  onChange(e) {
    console.log(e.detail.index)
    this.data.params.page = 1
    this.data.active = e.detail.index
    this.setData({
      active: e.detail.index
    })
    this.getList()
  },
  toSport(e){
    console.log(e)
    util.request(api.sportInfo,{inquirerId:e.currentTarget.dataset.id},'GET').then(res=>{
      if (res.data.code === 0){
        wx.navigateToMiniProgram({
          appId: 'wxa3a88c6a423e6931',
          extraData: {
            token: res.data.data.token
          },
          envVersion: 'release',
          success(res) {
            // 打开成功
          }
        })
      }else {
        Dialog.alert({
          message:res.data.msg
        })
      }
    })
  },
  toPay(e){
    wx.navigateTo({
      url: `/pages/sportOrderDetail/index?orderId=${e.currentTarget.dataset.id}&isDetail=true&isPay=true`
    })
  },
  toBuy(e){
    wx.navigateTo({
      url: `/pages/aiSport/index`
    })
  },
  /**
   *
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})