/* pages/sportOrderList/index.wxss */
page {
    display: flex;
    flex-direction: column;
    background: #f8f8f8;
    height: 100vh;
    overflow: hidden;
}
.list-container {
    padding: 24rpx 24rpx 40rpx;
    box-sizing: border-box;
    overflow: hidden;
    flex: 1;
}
.list{
    padding: 24rpx 24rpx 40rpx;
    box-sizing: border-box;
    flex: 1;
    overflow: scroll;
}
.list-items_1-0 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    display: flex;
    width: 100%;
    flex-direction: column;
    padding: 24rpx 24rpx 24rpx 24rpx;
}
.text_6-0 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 44rpx;
    margin-right: 558rpx;
}
.image-text_1-0 {
    flex-direction: row;
    display: flex;
    gap: 20rpx;
    margin: 16rpx 0 0 0;
    padding-bottom: 16rpx;
    box-sizing: border-box;
    width: 100%;
    border-bottom: 1px dashed #999999; /* 2px是虚线的高度，#000是颜色 */
}
.image_4-0 {
    width: 170rpx;
    height: 112rpx;
    margin-bottom: 8rpx;
}
.text-group_1-0 {
    margin-top: 8rpx;
    display: flex;
    flex-direction: column;
}
.text_7-0 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
}
.text_8-0 {
    overflow-wrap: break-word;
    color: rgba(255,75,40,1);
    font-size: 36rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 50rpx;
    margin: 22rpx 68rpx 0 0;
}
.image_5-0 {
    width: 654rpx;
    height: 2rpx;
    margin-top: 16rpx;
}
.group_2-0 {
    width: 100%;
    margin-top: 22rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.text_9-0 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    margin-top: 12rpx;
}
.text-wrapper_2-0 {
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    padding: 12rpx 46rpx 12rpx 46rpx;
    background: #eaeffc;
    border-radius: 20px;
}
.text_10-0 {
    overflow-wrap: break-word;
    color: rgba(23,80,220,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
}