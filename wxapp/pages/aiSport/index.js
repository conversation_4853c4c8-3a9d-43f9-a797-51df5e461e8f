// pages/aiSport/index.js
// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login'
          })
        }else{
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    }else{
      this.loadPage()
    }
  },

  loadPage(){
    // let url ='https://localhost:8066' + `/#/aiSportList?token=` + wx.getStorageSync('token')
    let url =api.WebViewUrl + `/#/aiSportList?token=` + wx.getStorageSync('token')
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})