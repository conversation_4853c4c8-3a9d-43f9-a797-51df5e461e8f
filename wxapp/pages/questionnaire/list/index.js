const api = require('../../../config/api')
const util = require('../../../utils/util')
const app = getApp()
Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '我的问卷',
    orderStatus: [
      { id: 0, name: '未填写' },
      { id: 1, name: '已填写' }
    ],
    statusBarHeight: null,
    activeColor: util.THEMECOLOR,
    activeIndex: 0,
    list: [],
    listQuery: {
      type: 0,
      page: 1,
      num: 10,
      patientId: null
    },
    nomes: api.ImgUrl + 'images/nomes.png'
  },
  onLoad(e) {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight,
      'listQuery.patientId': userInfo.userId
    })
    this.getList()
  },
  getList() {
    util.request(api.formPage, { ...this.data.listQuery }, 'GET').then((res) => {
      this.setData({
        list: res.data.data.result,
        loadComplete: !res.data.data.hasNext ? false : true
      })
    })
  },
  tabClick(event) {
    this.setData({
      'listQuery.type': event.currentTarget.dataset.id
    })
    if (wx.pageScrollTo) {
      wx.pageScrollTo({
        scrollTop: 0
      })
    }
    this.getList()
  },
  handleDetail(e) {
    const data = e.currentTarget.dataset.item
    wx.navigateTo({
      url: '/pages/questionnaire/detail/index?counselorFollowId=' + data.counselorFollowId + '&finishStatus=' + data.fillingStatus
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

    if (this.data.loadComplete) {
      this.setData({
        ['listQuery.page']: ++this.data.listQuery.page

      })
      this.getList()
    }
  }
})
