page {
  background: #f8f8f8;
}

.page-warp {
  background-color: #f8f8f8;
}

.banner {
  margin: 20rpx;
}

.banner image {
  display: block;
  width: 100%;
  height: 240rpx !important;
  border-radius: 8rpx;
  overflow: hidden;
}

.good-search {
  position: fixed;
  width: 100%;
  z-index: 9999;
}

.search-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 10rpx;
  flex-shrink: 0;
}

.cart {
  width: 44rpx;
  height: 44rpx;
  position: relative;
}

.cart .cart-icon {
  width: 100%;
  height: 100%;
}

.number {
  position: absolute;
  width: 30rpx;
  height: 30rpx;
  border-radius: 100%;
  top: -20rpx;
  right: -20rpx;
  background: red;
  color: #fff;
  font-size: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.content-class {
  padding: 0 !important;
  background: transparent !important;
}

.good-list {
  padding-bottom: 30rpx;
}

.pt-130 {
  padding-top: 130rpx;
  padding-bottom: 30rpx;
}

.load-more {
  text-align: center;
  margin: 20rpx auto;
  font-size: 26rpx;
  color: #aaa;
}

.relevant {
  position: fixed;
  left: 0;
  width: 100%;
  padding: 0 28rpx 28rpx 28rpx;
  background: #fff;
  z-index: 999;
  box-sizing: border-box;
  overflow: scroll;
}

.relevant-item {
  height: 100rpx;
  display: flex;
  align-items: center;
  border-bottom: 0.5rpx solid #eee;
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
}

.search-icon {
  width: 44rpx;
  height: 44rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
