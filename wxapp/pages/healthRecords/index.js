// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    inquirerId:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function() {
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const userInfo = wx.getStorageSync('userInfo')
    console.log(userInfo)
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        console.log(response)
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          let obj = {
            inquirerId:this.data.inquirerId
          }
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login?params=' + encodeURIComponent(JSON.stringify(obj))
          })
        }else {
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    }else{
      this.loadPage()
    }
  },

  loadPage(){
    // const url ='https://localhost:8066' + `#/medicalRecords?inquirerId=${this.data.inquirerId}&token=` + wx.getStorageSync('token')
    const url =api.WebViewUrl + `#/medicalRecords?inquirerId=${this.data.inquirerId}&token=` + wx.getStorageSync('token')
    console.log(url)
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
