<view class="container rel" bindtouchmove="touchMove">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" navTitle="{{navTitle}}"></navbar>
	<view class="bj">
		<image src="{{imgObject.background}}" mode="aspectFill" class="imgBlock"></image>
	</view>
	<view class="rel zx999 p30">
		<view class="doctorInfo  flex_c_m  bg-color-white p30">
			<view class="img">
				<image mode="aspectFill" src="{{doctorInfo.photo ? doctorInfo.photo : '/static/images/doctor_icon.png'}}" class="imgBlock">
				</image>
			</view>
			<view class="pl20 flex1">
				<view class="lh44 f32 b c333 mt10 pt2">{{doctorInfo.name}} <text
						class="n f28 c444 pl20">{{doctorInfo.title}}</text></view>
				<view class="f28 c666 lh40 pt5">{{doctorInfo.hospital}} | {{doctorInfo.department}}</view>
			</view>
		</view>
		<view class="evalBox bg-color-white mt32 p30">
			<view class="title f32 c333 b">问诊满意度</view>
			<view class="clearfix pt30">
				<view class="fl">
					<van-rate value="{{ value }}" readonly='{{exist}}' size="{{ 22 }}" color="#FFBA2B" void-color="#eee" void-icon="star"
						bind:change="onChange" />
				</view>
				<view class="fl c333 f28 b lh44" style="margin-left:60rpx">
					<block wx:if="{{value==1}}">非常不满意</block>
					<block wx:if="{{value==2}}">不满意</block>
					<block wx:if="{{value==3}}">一般</block>
					<block wx:if="{{value==4}}">满意</block>
					<block wx:if="{{value==5}}">非常满意</block>
				</view>
			</view>
			<view class="clearfix tag">
				<text wx:for='{{tags}}' bindtap='{{!exist ? "checkTag" : ""}}' data-index='{{index}}' data-check='{{item.check}}'
					data-name='{{item.name}}' class="{{item.check ? 'cur':''}}">{{item.name}}</text>
			</view>
			<view class="title f32 c333 b mt40">文字评价</view>
			<view class="textArea">
				<textarea name="" id="" cols="30" disabled='{{exist}}' value="{{content}}" rows="10" placeholder="向医生表达谢意或分享您的看病经验吧…" class="f28"
					placeholder-class="c999" maxlength="200" bindinput="textContent" class="c333"></textarea>
				<view class="tr c999 f24 textNum">
					{{content.length}}/200
				</view>
			</view>
			<view class="hideName f24 c666 radioCustom flex_m" wx:if="{{!exist}}">
				<van-checkbox value="{{ hideName }}" icon-size="28rpx" bind:change="hideNameFun">
					<text class="c666 f24 pl10">匿名提交</text>
				</van-checkbox>
			</view>
		</view>
	</view>
</view>
<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir">
	<view class="f24 c666 tc radioCustom">
		<view class="flex_c_m" wx:if="{{!exist}}">
			<van-checkbox value="{{ agree }}" icon-size="28rpx" bind:change="agreeFun">
				<text class="c666 f24 pl10">我已仔细阅读并同意 </text>	<text catchtap='goPage' class="color-primary" data-path='/pages/agreement/index' data-type='7'>《发布条款》</text>
			</van-checkbox>

		</view>
	</view>
	<button class="mt20" bindtap='submit' disabled="{{!agree || exist}}">{{exist?'已提交':'提交'}}</button>
</view>