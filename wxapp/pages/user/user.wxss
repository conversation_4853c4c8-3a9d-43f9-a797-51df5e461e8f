/* pages/user/user.wxss */
page {
  background: #f8f8f8;
}
.bgImg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 500rpx;
}
.userInfo {
  padding-left: 60rpx;
  padding-top: 30rpx;
}
.userInfo .photo {
  float: left;
  width: 136rpx;
  height: 136rpx;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  padding: 4rpx;
}
.userInfo .avatar {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;
}
.orderMenu {
  margin-top: 86rpx;
}
.orderMenu2 {
  margin-top: 10rpx;
}
.br20 {
  border-radius: 20rpx;
}
.more {
  color: #8b8b8b;
}
.more image {
  display: inline-block;
  width: 20rpx;
  height: 44rpx;
  vertical-align: top;
}
.menu_warp .item {
  width: 25%;
  float: left;
}
.menu_warp .item .icon {
  width: 100%;
  text-align: center;
  width: 50rpx;
  height: 50rpx;
  margin: 0 auto;
  margin-top: 8rpx;
  position: relative;
}
.menu_warp .item .icon text {
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: var(--redColor);
  line-height: 32rpx;
  color: #fff;
  font-size: 22rpx;
  font-weight: bold;
  top: -12rpx;
  left: 32rpx;
}
.menu_warp .item .icon text.cur {
  width: auto;
  padding: 0 8rpx;
  border-radius: 19rpx;
}
.menu_warp .item .icon image {
  display: block;
  width: 50rpx;
  height: 50rpx;
}
.menu_warp .item view.name {
  padding-top: 8rpx;
}
.menu .item .icon {
  float: left;
}
.menu .item text {
  float: left;
  display: block;
  height: 44rpx;
  margin-left: 16rpx;
}
.menu .item image {
  display: block;
  float: right;
  width: 44rpx;
  height: 44rpx;
}
.real_authent {
  max-width: 300rpx;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8rpx;
  border: 1rpx solid #df4634;
  display: flex;
  align-items: center;
  padding: 12rpx;
  margin-top: 8rpx;
}
.real_text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #df4634;
  margin-right: 8rpx;
}
.icon_more {
  width: 40rpx;
  height: 44rpx;
}
.mod {
  width: 368rpx;
  height: 48rpx;
  background: #ddefff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  position: relative;
  /* top: 40rpx; */
  top: -15rpx;
}
.tips {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #367dff;
}
.mod:before {
  content: '';
  width: 0px;
  height: 0px;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 10px solid #ddefff;
  position: absolute;
  top: -10px;
  left: 28px;
}
.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content .title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  text-align: center;
  margin-top: 60rpx;
}

.content .photo {
  background-color: #fff;
  overflow: hidden;
  height: 160rpx;
  margin-top: 100rpx;
  margin-bottom: 60rpx;
}

.content .avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
}

.nick {
  background: #ffffff;
  border-radius: 48rpx;
  border: 2rpx solid #dddddd;
  margin: 0 60rpx;
  height: 96rpx;
}

.nick input {
  width: 100%;
  height: 100%;
  text-align: left;
  padding-left: 60rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
}

.btn {
  height: 96rpx;
  background: #367dff;
  border-radius: 48rpx;
  margin: 100rpx 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.confirm {
  font-weight: bold !important;
  font-size: 32rpx !important;
  color: #ffffff !important;
  border: none !important;
  width: 100%;
}

.contacButton {
  background: none;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  height: 44rpx;
  width: 100%;
}

.justify-between {
  justify-content: space-between;
}