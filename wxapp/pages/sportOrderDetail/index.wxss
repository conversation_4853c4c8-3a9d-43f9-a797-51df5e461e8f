.page {
    background-color: rgba(247,247,247,1.000000);
    position: relative;
    width: 750rpx;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}
.block_1 {
    background: url(https://lanhu-oss.lanhuapp.com/SketchPng2c338314ddebaeb8736e8ae8f4b9f0c92aa81587345966b52fcd414d7d0c7fe2) 100% no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    padding: 28rpx 0 0 24rpx;
}
.group_1 {
    flex-direction: row;
    display: flex;
    margin: 0 32rpx 0 68rpx;
}
.text_1 {
    overflow-wrap: break-word;
    color: rgba(38,38,38,1);
    font-size: 30rpx;
    letter-spacing: -1rpx;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 36rpx;
}
.thumbnail_1 {
    width: 34rpx;
    height: 22rpx;
    margin: 8rpx 0 6rpx 436rpx;
}
.thumbnail_2 {
    width: 30rpx;
    height: 22rpx;
    margin: 6rpx 0 8rpx 10rpx;
}
.image_1 {
    width: 48rpx;
    height: 22rpx;
    margin: 6rpx 0 8rpx 10rpx;
}
.group_2 {
    margin-top: 24rpx;
    flex-direction: row;
    display: flex;
}
.label_1 {
    width: 48rpx;
    height: 48rpx;
    margin: 20rpx 0 20rpx 0;
}
.text_2 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 44rpx;
    margin: 20rpx 0 0 238rpx;
}
.image_2 {
    width: 200rpx;
    height: 88rpx;
    margin-left: 112rpx;
}
.block_2 {
    height: 100vh;
    overflow: hidden;
    padding-bottom: 190rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}
.block_2_auto {
    height: 100vh;
    overflow: auto;
    padding-bottom: 190rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}
.group_3 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: NaNrpx;
    width: 100%;
    flex-direction: row;
    display: flex;
    padding: 32rpx 32rpx 30rpx 32rpx;
}
.group_4 {
    margin-bottom: 2rpx;
    display: flex;
    flex-direction: column;
}
.card_1{
    padding: 8rpx 16rpx;color:#999999;border-radius: 8px;border: 2px solid #EBEBEB;
}
.card_2{
    padding: 8rpx 16rpx;color:#1750DC;border-radius: 8px;border: 1px solid #1750DC;
    background: #1750DC14;
}
.text_3 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 44rpx;
    margin-right: 12rpx;
}
.text-wrapper_1 {
    width: 108rpx;
    margin-top: 16rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.text_4 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 36rpx;
}
.text_5 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 36rpx;
}
.group_5 {
    display: flex;
    flex-direction: column;
    margin: 2rpx 0 0 4rpx;
}
.text-wrapper_2 {
    background-color: rgba(23,80,220,0.100000);
    border-radius: NaNrpx;
    margin-right: 106rpx;
    display: flex;
    flex-direction: column;
    padding: 2rpx 10rpx 2rpx 10rpx;
}
.text_6 {
    overflow-wrap: break-word;
    color: rgba(23,80,220,1);
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 36rpx;
}
.text_7 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 36rpx;
    margin: 20rpx 0 0 20rpx;
}
.image-text_1 {
    flex-direction: row;
    display: flex;
    justify-content: space-between;
    align-items: center;
    /*margin: 28rpx;*/
    margin-left: auto;
}
.text-group_1 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 26rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 36rpx;
}
.thumbnail_3 {
    width: 40rpx;
    height: 40rpx;
}
.group_6 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: 16rpx;
    align-self: center;
    margin-top: 24rpx;
    width: 702rpx;
    display: flex;
    flex-direction: column;
    padding: 24rpx 220rpx 24rpx 24rpx;
}

.text-wrapper_4_1 {
    width: 654rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.text_10_1 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 44rpx;
}
.text_11_1 {
    overflow-wrap: break-word;
    color: rgba(255,75,40,1);
    font-size: 24rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 34rpx;
    margin-top: 10rpx;
}

.text-wrapper_3 {
    margin-right: 330rpx;
    display: flex;
    flex-direction: row;
}
.text_8 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 44rpx;
}
.section_1 {
    width: 458rpx;
    margin-top: 16rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.image-text_2 {
    width: 304rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.image_3 {
    width: 170rpx;
    height: 112rpx;
}
.text-group_2 {
    display: flex;
    flex-direction: column;
}
.text_9 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    margin-left: 6rpx;
    white-space: nowrap;
    line-height: 40rpx;
}
.text_10 {
    overflow-wrap: break-word;
    color: rgba(255,75,40,1);
    font-size: 36rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: left;
    white-space: nowrap;
    line-height: 50rpx;
    margin: 22rpx 12rpx 0 0;
}
.text-wrapper_4 {
    background-color: rgba(255,165,18,0.100000);
    border-radius: NaNrpx;
    margin-bottom: 72rpx;
    display: flex;
    flex-direction: column;
    padding: 4rpx 8rpx 2rpx 8rpx;
}
.text_11 {
    overflow-wrap: break-word;
    color: rgba(255,165,18,1);
    font-size: 24rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 34rpx;
}
.group_7 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: 16rpx;
    width: 702rpx;
    align-self: center;
    margin-top: 24rpx;
    display: flex;
    flex-direction: column;
    padding: 24rpx 24rpx 24rpx 24rpx;
}
.list_1 {
    height: 96rpx;
    flex-direction: row;
    margin-top: 16rpx;
    display: flex;
}
input{
    border: 1rpx #EBEBEb solid;
    border-radius: 10rpx;
    width: 100%;
    height: 60rpx;
    padding-right: 20rpx;
    padding-left: 20rpx;
    box-sizing: border-box;
    font-size: 24rpx;
}
textarea{
    border: 1rpx #EBEBEB solid;
    border-radius: 10rpx;
    width: 100%;
    padding: 10rpx;
    box-sizing: border-box;
    font-size: 24rpx;
}
.list_1_1 {
    height: 96rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.text-wrapper_5-0 {
    display: flex;
    flex-direction: column;
    font-family: PingFangSC, PingFang SC;
}
.text_12-0 {
    width: 158rpx;
    overflow-wrap: break-word;
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
    margin-top: 10rpx;
    color: rgba(153,153,153,1);
}
.text_13-0 {
    width: 158rpx;
    overflow-wrap: break-word;
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
    margin-top: 30rpx;
    color: rgba(153,153,153,1);
}
.text-wrapper_5-1 {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 10rpx;
}
.text_12-1 {
    overflow-wrap: break-word;
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    color: rgba(51,51,51,1);
}
.text_13-1 {
    overflow-wrap: break-word;
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    margin-top: 16rpx;
    color: rgba(51,51,51,1);
}
.text-wrapper_6 {
    width: 100%;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-top: 50rpx;
}
.text-wrapper_6_1 {
    width: 100%;
    flex-direction: column;
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-top: 50rpx;
}

.text_14 {
    overflow-wrap: break-word;
    color: rgba(153,153,153,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
}
.text_15 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
}
.text-wrapper_7 {
    margin-top: 16rpx;
    flex-direction: row;
    display: flex;
    justify-content: space-between;
}
.text_16 {
    overflow-wrap: break-word;
    color: rgba(153,153,153,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 40rpx;
}
.text_17 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 40rpx;
}
.text-group_3 {
    width: 654rpx;
    align-self: center;
    margin-top: 60rpx;
    display: flex;
    flex-direction: column;
}
.text_18 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    align-self: center;
}
.text_19 {
    width: 654rpx;
    height: 80rpx;
    overflow-wrap: break-word;
    color: rgba(153,153,153,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: center;
    line-height: 40rpx;
    margin-top: 24rpx;
}
.image-text_3 {
    width: 200rpx;
    align-self: center;
    margin-top: 32rpx;
    display: flex;
    flex-direction: column;
}
.group_8 {
    background-color: rgba(216,216,216,1.000000);
    width: 200rpx;
    height: 200rpx;
    display: flex;
    flex-direction: column;
}
.text-group_4 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    margin: 24rpx 46rpx 0 42rpx;
}
.block_3 {
    height: 180rpx;
    width: 100%;
    background: #FFFFFF;
    display: flex;
    padding: 16rpx 30rpx 84rpx 32rpx;
    flex-direction: column;
    gap: 20rpx;
    position: fixed;
    bottom: 0;
    left: 0;
}
.text_20 {
    width: 686rpx;
    height: 80rpx;
    border-radius: 40rpx;
    color: #ffffff;
    background: #1750DC;
    padding: 16rpx 274rpx 16rpx 276rpx;
}

.text_20_1 {
    width: 320rpx;
    overflow-wrap: break-word;
    color: rgba(255,255,255,1);
    font-size: 34rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: right;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1750DC14;
    color: #1750DC;
    height: 80rpx;
    border-radius: 40rpx;
}

.text_20_2 {
    width: 320rpx;
    overflow-wrap: break-word;
    color: rgba(255,255,255,1);
    font-size: 34rpx;
    font-family: PingFangSC-Medium;
    font-weight: 500;
    text-align: right;
    background: #1750DC;
    height: 80rpx;
    border-radius: 40rpx;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image_4 {
    position: absolute;
    left: -2rpx;
    top: 120rpx;
    width: 750rpx;
    height: 60rpx;
}