// pages/from/fromDetail/index.js
import toast from "../../lib/vant-weapp/toast/toast";

const api = require('../../config/api.js')
const util = require('../../utils/util.js')
import Dialog from '../../lib/vant-weapp/dialog/dialog'

Page({

    /**
     * 页面的初始数据
     */
    data: {
        isBack: true,
        backgroundColor: '#fff',
        navTitle: '订单详情',
        orderId: '',
        mc: '',
        id: '',
        amount: '',
        validTime: '',
        description: '',
        imgPath: '',
        counselorQrCodeUrl: '',
        intro: '',
        name: '',
        tags: '',
        code: '',
        createdAt: '',
        orderInfo: '',
        isDetail: false,
        checked: false,
        isAgreement: false,
        conClass:'block_2',
        userInfo: {},
        selected: [],
        inquirerId: '',
        statusText: ['未支付', '已支付', '已取消', '已退款'],
        diseaseText:'',
        height:'',
        weight:'',
        disease:[
            { name: '勃起功能障碍', class: 'card_1' },
            { name: '高血脂', class: 'card_1' },
            { name: '高血压', class: 'card_1' },
            { name: '高尿酸血症', class: 'card_1' },
            { name: '不育症', class: 'card_1' },
            { name: '慢性盆腔疼痛综合症', class: 'card_1' },
            { name: 'LOH', class: 'card_1' },
            { name: '早泄', class: 'card_1' },
            { name: '抑郁状态', class: 'card_1' },
            { name: '焦虑状态', class: 'card_1' },
            { name: '性欲低下', class: 'card_1' },
            { name: '前列腺炎', class: 'card_1' },
            { name: '糖尿病', class: 'card_1' },
            { name: '冠心病', class: 'card_1' }
        ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        this.data.orderId = options.orderId
        this.data.amount = options.amount
        this.data.description = decodeURI(options.description)
        this.data.id = options.id
        this.data.imgPath = options.imgPath
        this.data.code = options.code
        this.data.intro = decodeURI(options.intro)
        this.data.name = decodeURI(options.name)
        this.data.tags = decodeURI(options.tags)
        this.data.isDetail = options.isDetail
        if (this.data.isDetail){
            this.data.conClass = 'block_2_auto'
        }
        this.ageePopup = this.selectComponent('#ageePopup') //his就诊记录弹窗
        this.ageePopup.getArticle()
        this.setData({
            amount:this.data.amount,
            description:decodeURI(this.data.description),
            id:this.data.id,
            imgPath:decodeURI(this.data.imgPath),
            intro:this.data.intro,
            name:this.data.name,
            selected:[],
            conClass:this.data.conClass,
            tags:this.data.tags,
            checked:this.data.checked,
            isDetail:this.data.isDetail,
        })
    },
    handleShowAgm() {
        this.setData({ showPopup: true })
        this.ageePopup.setData({
            showPopup: true
        })
    },
    onChecked(e) {
        if (this.data.isAgreement) {
            this.setData({
                checked: e.detail
            })
        } else {
            // this.ageePopup.getArticle(6)
            this.ageePopup.setData({
                showPopup: true
            })
            this.setData({ showPopup: true })
        }
        console.log(this.ageePopup.data.showPopup, e, 'this.ageePopup.data.showPopup')
    },
    // 用户同意协议
    onAgreement() {
        this.setData({
            checked: true,
            isAgreement: true,
            showPopup: false
        })
        console.log('同意了')
    },
    onclosePopup() {
        const { isAgreement } = this.data
        this.setData({
            showPopup: false,
            checked: isAgreement ? true : false
        })
    },
    async toPay() {
        const res = await util.request(api.getPayInfo + '/' + this.data.orderId, {}, 'get')
        if (res.data.code === 100) {
            console.log(res.data.code, 'res.data.code')
            await Dialog.confirm({
                message: res.data.msg
            })
            return
        }
        wx.requestPayment({
            ...res.data.data,
            success: () => {
                wx.navigateTo({
                    url: `pages/sportPayDone/index?orderId=${this.data.orderId}`
                })
            },
            fail: () => {
            }
        })
    },
    buildQueryString(params) {
        const pairs = [];
        for (const [key, value] of Object.entries(params)) {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
        return pairs.join('&');
    },

    toSport() {
            util.request(api.sportInfo, {inquirerId: this.data.userInfo.inquirerId}, 'get')
                .then(res => {
                    console.log(res.data)
                })
    },

    inputHeight(e) {
        this.handleInput('height', e.detail.value);
    },
    inputWeight(e) {
        this.handleInput('weight', e.detail.value);
    },
    handleInput(field, value) {
        if (!value) {
            this.setData({ [field]: '' }); // 清空字段值
            return;
        }
        if (!/^\d*\.?\d*$/.test(value)) {
            console.log('输入的不是数字');
            this.setData({ [field]: '' }); // 清空字段值
            return; // 如果不是数字，直接返回
        }
        // 保留一位小数，不进行四舍五入
        const formattedValue = this.formatDecimal(value);

        // 更新数据
        this.setData({ [field]: formattedValue });
        console.log(`更新后的 ${field}:`, this.data[field]);
    },
    formatDecimal(value) {
        // 将字符串转换为数字
        const number = Number(value);
        console.log(number)

        // 如果是整数，补充一位小数（如 1 -> 1.0）
        if (Number.isInteger(number)) {
            return number.toFixed(1);
        }

        // 如果是小数，保留一位小数，不四舍五入
        const integerPart = Math.floor(number); // 整数部分
        const decimalPart = String(number).split('.')[1]; // 小数部分
        const firstDecimal = decimalPart ? decimalPart[0] : '0'; // 取第一位小数

        return `${integerPart}.${firstDecimal}`;
    },
    inputDisease(e){
        this.data.diseaseText = e.detail.value
    },

    selectDisease(e) {
        const index = e.currentTarget.dataset.index;
        if (this.data.disease[index].class === 'card_1'){
            this.data.disease[index].class = 'card_2'
        }else {
            this.data.disease[index].class = 'card_1'
        }
        // 更新 selected 数组
        this.setData({
            disease: this.data.disease
        });
    },

    initData() {
        util.request(api.peopleList, {}, 'post').then(userInfo => {
            if (userInfo.data.code === 0) {
                // this.data.userInfo = userInfo.data.data[0]
                if (this.data.inquirerId){
                    this.data.userInfo = userInfo.data.data.filter(item=>{
                        return item.inquirerId === this.data.inquirerId
                    })[0]
                }else {
                    this.data.userInfo = userInfo.data.data[0]
                }
                console.log(this.data.userInfo,this.data.inquirerId)
                this.setData({
                    userInfo: this.data.userInfo,
                })
            }
        })
    },

    cancelOrder() {
        Dialog.confirm({
            message: '确定要取消该订单吗？'
        }).then(() => {
            util.request(api.mgCancelOrder + '?orderId=' + this.data.orderInfo.id, {}, 'post').then(res => {
                if (res.code === 0) {
                    this.initData()
                } else {
                    Dialog.confirm({
                        message: res.data.msg
                    })
                }
            })
        })
    },

     createOrder(){
        if (!this.data.userInfo.idCard){
            Dialog.alert({
                message:'当前就诊人信息未实名，请先进行实名认证!'
            })
            return
        }
        if (!this.data.checked){
            return Dialog.alert({
                message:'请先阅读并同意《AI运动处方服务协议》!'
            })
        }
        if (!this.data.height){
            return Dialog.alert({
                message:'请先填写身高数据!'
            })
        }
        if (!this.data.weight){
            return Dialog.alert({
                message:'请先填写体重数据!'
            })
        }
        const disease1 = this.data.disease.filter(item=>{
            return item.class === 'card_2'
        })
        if (!this.data.diseaseText && disease1.length === 0){
            return Dialog.alert({
                message:'请先填写或选择病情描述!'
            })
        }
        let diseaseT = ''
        if (this.data.diseaseText && disease1.length > 0){
            diseaseT = this.data.diseaseText +','+ disease1.map(item=>item.name).join(',')
        }else if (this.data.diseaseText && disease1.length === 0){
            diseaseT = this.data.diseaseText
        }else {
            diseaseT = disease1.map(item=>item.name).join(',')
        }
        const param = {
            disease:diseaseT,
            height:Number(this.data.height),
            weight:Number(this.data.weight),
            inquirerId:Number(this.data.userInfo.inquirerId),
            sportPrescriptionId:Number(this.data.id)
        }
        util.request(api.sportOrder,{...param},'post').then(res=>{
            if (res.data.code === 100) {
                Dialog.confirm({
                    message: res.data.msg
                })
                return
            }
            util.request(api.getSportPayInfo + '/' + res.data.data.orderId, {
            }, 'get').then(res2=>{
                if (res2.data.code === 100) {
                    console.log(res2.data.code, 'res.data.code')
                    Dialog.confirm({
                        message: res2.data.msg
                    })
                    return
                }
                wx.requestPayment({
                    ...res2.data.data,
                    success: () => {
                        wx.navigateTo({
                            url: `/pages/sportPayDone/index?orderId=${res.data.data.orderId}`
                        })
                    },
                    fail: () => {
                    }
                })
            })
            // wx.requestPayment({
            //     ...res.data.data,
            //     success: () => {
            //         wx.navigateTo({
            //             url: `pages/mgPayDone/index?orderId=${this.data.orderId}`
            //         })
            //     },
            //     fail: () => {
            //     }
            // })
        })
    },

    reOrder() {
        this.data.isDetail = false
        this.data.orderId = ''
    },

    selectUser() {
        if (this.data.isDetail) return
        wx.navigateTo({
            url: '/pages/peopleContent/people/people?from=SPORT'
        })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
        console.log(1213213)
        if (this.data.orderId && this.data.isDetail){
            util.request(`${api.sportOrderDetail}${this.data.orderId}`,{},'GET').then(res=>{
                this.data.amount = res.data.data.amount
                this.data.description = res.data.data.description
                this.data.id = res.data.data.id
                this.data.imgPath = res.data.data.imgPath
                this.data.counselorQrCodeUrl = res.data.data.counselorQrCodeUrl
                this.data.intro = res.data.data.intro
                this.data.name = res.data.data.prescriptionName
                this.data.inquirerId = res.data.data.inquirerId
                this.data.tags = res.data.data.tags
                this.data.validTime = res.data.data.validTime
                this.data.createdAt = res.data.data.createdAt
                this.data.height = res.data.data.height
                this.data.weight = res.data.data.weight
                this.data.diseaseText = res.data.data.disease
                this.initData()
                console.log(this.data.createdAt)
                console.log(res.data.data.createdAt)
                this.setData({
                    amount:this.data.amount,
                    description:this.data.description,
                    counselorQrCodeUrl:this.data.counselorQrCodeUrl,
                    id:this.data.id,
                    imgPath:this.data.imgPath,
                    intro:this.data.intro,
                    height:this.data.height,
                    weight:this.data.weight,
                    name:this.data.name,
                    tags:this.data.tags,
                    validTime:this.data.validTime,
                    createdAt:this.data.createdAt,
                    diseaseText:this.data.diseaseText,
                })
            })
        }else {
            this.initData()
        }
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})
