<view class="page">
    <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <view class="{{conClass}}">
        <view class="group_3">
            <view class="group_4">
                <text lines="1" class="text_3">{{userInfo.name}}</text>
                <view class="text-wrapper_1">
                    <text lines="1" class="text_4">{{ userInfo.gender === 1?'男':'女' }}</text>
                    <text lines="1" class="text_5">{{ userInfo.age }}</text>
                </view>
            </view>
            <view class="group_5">
                <view class="text-wrapper_2">
                    <text lines="1" class="text_6">{{ userInfo.relationName }}</text>
                </view>
                <text lines="1" class="text_7">{{ userInfo.phone }}</text>
            </view>
            <view class="image-text_1" bind:tap="selectUser">
                <text lines="1" class="text-group_1">切换就诊人</text>
                <image src="/static/images/icon_arrow.png" class="thumbnail_3"></image>
            </view>
        </view>
        <view class="group_6">
            <view class="text-wrapper_4_1">
                <text lines="1" class="text_10_1">商品信息</text>
            </view>
            <view class="section_1">
                <view class="image-text_2">
                    <image src="{{imgPath}}" class="image_3"></image>
                    <view class="text-group_2">
                        <text lines="1" class="text_9">{{name}}</text>
                        <text lines="1" class="text_10">￥{{ amount }}</text>
                    </view>
                </view>
            </view>
        </view>
        <view class="group_7" wx-if="{{isDetail}}">
            <view class="text-wrapper_4_1">
                <text lines="1" class="text_10_1">健康信息</text>
            </view>
            <view class="list_1">
                <view class="text-wrapper_5-0">
                    <text lines="1" class="text_12-0">身高</text>
                    <text lines="1" class="text_13-0">体重</text>
                </view>
                <view class="text-wrapper_5-1">
                    <text lines="1" class="text_12-1">{{ height }}</text>
                    <text lines="1" decode="true" class="text_13-1">{{ weight }}</text>
                </view>
            </view>
            <view class="text-wrapper_6">
                <text lines="1" class="text_14">病情描述</text>
                <text lines="1" class="text_15">{{ diseaseText }}</text>
            </view>
        </view>
        <view class="group_7" wx-if="{{isDetail}}">
            <view class="list_1_1">
                <view class="text-wrapper_5-0">
                    <text lines="1" class="text_12-0">订单编号</text>
                    <text lines="1" class="text_13-0">下单时间</text>
                </view>
                <view class="text-wrapper_5-1">
                    <text lines="1" class="text_12-1">{{ id }}</text>
                    <text lines="1" decode="true" class="text_13-1">{{ createdAt }}</text>
                </view>
            </view>
            <view class="text-wrapper_6">
                <text lines="1" class="text_14">订单金额</text>
                <text lines="1" class="text_15">￥{{ amount }}</text>
            </view>
            <view class="text-wrapper_7">
                <text lines="1" class="text_16">有效期</text>
                <text lines="1" class="text_17">{{ validTime || '--' }}</text>
            </view>
        </view>
        <view class="group_7" wx-if="{{!isDetail}}" style="flex: 1;overflow: auto">
            <view class="text-wrapper_4_1">
                <text lines="1" class="text_10_1">健康信息</text>
            </view>
            <view class="list_1">
                <view class="text-wrapper_5-0">
                    <text lines="1" class="text_12-0">身高</text>
                    <text lines="1" class="text_13-0">体重</text>
                </view>
                <view class="text-wrapper_5-1">
                    <view style="display: flex;gap: 4rpx;position: relative;align-items: center">
                        <input type="digit" value="{{height}}" bindblur="inputHeight"></input><view style="position: absolute;top: 0;right: 10rpx">cm</view>
                    </view>
                    <view style="display: flex;gap: 4rpx;position: relative">
                        <input type="digit" value="{{weight}}" bindblur="inputWeight"></input><view style="position: absolute;top: 0;right: 10rpx">kg</view>
                    </view>
                </view>
            </view>
            <view class="text-wrapper_6_1">
                <text lines="1" class="text_14">病情描述</text>
                <textarea  bindinput="inputDisease" placeholder="请输入您的线下诊断"></textarea>
                <view style="display: flex;gap: 4rpx;flex-wrap: wrap;margin-top: 10rpx">
                    <view wx:for="{{disease}}" bind:tap="selectDisease" data-index="{{index}}" class="{{item.class}}">{{item.name}}</view>
                </view>
            </view>
        </view>
        <view class="text-group_3" wx-if="{{isDetail}}">
            <text lines="1" class="text_18">售后咨询</text>
            <text lines="1" class="text_19">如您有数字疗法相关产品使用和内容设置等问题，可识别下方二维码联系心理健康管理师</text>
        </view>
        <view class="image-text_3" wx-if="{{isDetail}}">
            <view class="group_8">
                <image src="{{counselorQrCodeUrl}}" show-menu-by-longpress style="width: 100%;height: 100%;object-fit: cover"></image>
            </view>
            <text lines="1" class="text-group_4">长按识别</text>
        </view>
    </view>
    <view class="block_3" wx-if="{{!isDetail}}">
        <van-checkbox value="{{ checked }}" shape="square" bind:change="onChecked" data-type="checked" icon-size="28rpx"
                      custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='17'
                                                                                           bind:tap="handleShowAgm">《AI运动处方服务协议》</text>
            <!-- <text class="f24 color-primary" data-type='5'
                      catchtap='goAgreement'>《{{company}}互联网医院服务协议》</text> -->
        </van-checkbox>
        <view wx:if="{{validTime > 0}}" class="text_20" bind:tap="toSport">去运动</view>
        <view wx:if="{{validTime == 0 && isDetail}}" class="text_20" bind:tap="reOrder">再次购买</view>
        <view class="text_20" wx-if="{{!isDetail}}" bind:tap="createOrder">提交</view>
    </view>
</view>
<agreement-popup id="ageePopup" type='17' bind:agreement='onAgreement' bind:closePopup='onclosePopup'></agreement-popup>
<van-dialog id="van-dialog" confirm-button-color="#367DFF" />