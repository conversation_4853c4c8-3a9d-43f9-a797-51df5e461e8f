var api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '智能导诊',
    active: 0,
    symptomId: '',
    keywords: '',
    symptomInfo: {},
    departmentList: [],
    symptomItems: [
      { title: '主要病因', content: '' },
      { title: '常见症状', content: '' },
      { title: '检查项目', content: '' },
      { title: '发病部位', content: '' },
      { title: '重要提醒', content: '' },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options)
    this.setData({
      symptomId: options.id,
      keywords: options.keywords
    }, () => {
      this.getSymptomDetail()
    })
  },

  // 获取症状详情
  getSymptomDetail() {
    let that = this
    console.log(that.data.keywords);
    util.request(api.symptomDetail + that.data.symptomId + `?keywords=${that.data.keywords}`, {}, 'get').then(res => {
      if (res.data.code === 0) {
        
        that.setData({
          symptomInfo: res.data.data,
          departmentList: res.data.data.departmentMappings,
          'symptomItems[0].content': res.data.data.primaryCause || '',
          'symptomItems[1].content': res.data.data.commonSymptoms || '',
          'symptomItems[2].content': res.data.data.diagnosticTests || '',
          'symptomItems[3].content': res.data.data.affectedArea || '',
          'symptomItems[4].content': res.data.data.importantNotes || ''
        })
      } else {
        wx.showToast({
          title: '获取症状详情失败',
          icon: 'none'
        })
      }
    })
  },

  goDepartment(event) {
    const { departmentId, departmentName } = event.currentTarget.dataset.item
    if (!departmentId) return
    wx.navigateTo({
      url: `/pages/doctorList/index?departmentId=${departmentId}&text=${departmentName}`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})