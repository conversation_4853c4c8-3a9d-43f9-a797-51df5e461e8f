<view class="container">
    <view class="header">
        <navbar isBack="{{isBack}}" home="{{!isBack}}" navTitle="{{navTitle}}"></navbar>
        <view class="desc">
            <view class="text1">智能导诊</view>
            <view class="text2" style="margin-right:10rpx;">描述疾病</view>
            <view class="text3">推荐科室</view>
        </view>
        <view class="symptom-name">{{ symptomInfo.symptom }}</view>
        <view wx:if="{{symptomInfo.symptomAlias}}" class="alias">其它名称：{{ symptomInfo.symptomAlias }}</view>
        <view class="department">就诊科室：<text class="{{item.departmentId ? 'under-line' : '' }}" wx:for="{{departmentList}}" wx:key="{{item.id}}" data-item="{{item}}" bindtap="goDepartment">{{item.departmentName}}</text></view>
    </view>
    <!-- tab -->
    <!-- <van-tabs active="{{ active }}" color="#367DFF" line-width="28" sticky bind:change="onChange">
        <van-tab title="主要病因">主要病因</van-tab>
        <van-tab title="常见症状">常见症状</van-tab>
        <van-tab title="检查项目">检查项目</van-tab>
        <van-tab title="发病部位">发病部位</van-tab>
        <van-tab title="重要提示">重要提示</van-tab>
    </van-tabs> -->
    <!-- 内容区域 -->
    <view class="content-box">
        <view wx:for="{{symptomItems}}" wx:key="index" wx:if="{{item.content}}" class="content">
            <view class="content-name">{{item.title}}</view>
            <!-- <view class="content-line"></view> -->
            <view class="content-text">
                {{item.content}}
            </view>
        </view>
    </view>
</view>