page {
  background: #f8f8f8;
}

.container .header {
  position: relative;
  /* height: 450rpx; */
  padding-bottom: 28rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-size: contain;
  background: url('https://patient.moonhealth.cn/images/home/<USER>') no-repeat;
}

.desc {
  width: 336rpx;
  height: 48rpx;
  background: #e2ecff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  margin: 28rpx;
}

.text1 {
  width: 120rpx;
  height: 48rpx;
  background: linear-gradient(90deg, #367dff 0%, #70a3ff 100%);
  border-radius: 8rpx;
  font-weight: 500;
  font-size: 22rpx;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}

.text2,
.text3 {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 22rpx;
  color: #367dff;
}

.symptom-name {
  font-weight: 600;
  font-size: 46rpx;
  color: #333333;
  margin-left: 28rpx;
  font-family: PingFangSC, PingFang SC;
}

.department {
  font-weight: 600;
  font-size: 28rpx;
  color: #002f85;
  margin-top: 12rpx;
  margin-left: 28rpx;
  margin-right: 28rpx;
}

.department text {
  margin-right: 24rpx;
  line-height: 40rpx;
}

.under-line {
  text-decoration: underline;
}

.alias {
  margin-top: 12rpx;
  margin-left: 28rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #002f85;
}

.content {
  margin: 28rpx 28rpx;
}

.content-box {
  padding-bottom: 50rpx;
}

.content-name {
  position: relative;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.content-line {
  width: 24rpx;
  height: 6rpx;
  background: #333333;
  border-radius: 4rpx;
  margin: 15rpx 0 28rpx 4rpx;
}

.content-text {
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
  text-align: justify;
}
