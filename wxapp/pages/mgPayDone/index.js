// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
const {MgViewUrl} = require("../../config/api-prod");
Page({

    /**
     * 页面的初始数据
     */
    data: {
        orderId: '',
        orderInfo: '',
        userInfo: ''
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad: function (options) {
        const orderId = options.orderId
        util.request(api.mgOrderDetail + '/' + this.data.orderId, {}, 'get').then(res => {
            if (res.data.code === 0) {
                this.data.orderInfo = res.data.data
            }
        })
    },

    toOrder() {
        wx.navigateTo({
            url: '/pages/mgOrderDetail/index?orderId=' + this.data.orderId
        })
    },
    buildQueryString(params) {
        const pairs = [];
        for (const [key, value] of Object.entries(params)) {
            pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
        }
        return pairs.join('&');
    },

    toMG() {
        // /ap/mg/product/info
        util.request(api.mgInfo, { inquirerId: this.data.userInfo.inquirerId }, 'get')
            .then(mgInfo => {
                let url = `${MgViewUrl}?top=0&bottom=0&pt=${mgInfo.data.data.partnerToken}&pid=${mgInfo.data.data.partnerId}&from=mini&mc=insomnia`
                const params = encodeURIComponent(JSON.stringify(url))
                wx.navigateTo({
                    url: `/pages/webView/index?url=${params}`
                })
            })
    },

    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {
    },

    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {
    },

    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {
    },

    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {

    }
})
