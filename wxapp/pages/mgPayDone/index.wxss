.page {
    background-color: rgba(247,247,247,1.000000);
    position: relative;
    width: 750rpx;
    height: 100%;
    overflow: hidden;
    padding-left: 0rpx;
    padding-right: 0rpx;
    padding-top: 0rpx;
    padding-bottom: 88rpx;
    display: flex;
    flex-direction: column;
}
.group_1 {
    background: url(https://lanhu-oss.lanhuapp.com/SketchPng2c338314ddebaeb8736e8ae8f4b9f0c92aa81587345966b52fcd414d7d0c7fe2) 100% no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    padding: 32rpx 28rpx 12rpx 14rpx;
}
.group_2 {
    margin-left: 80rpx;
    flex-direction: row;
    display: flex;
}
.text-wrapper_1 {
    width: 56rpx;
    height: 34rpx;
    overflow-wrap: break-word;
    font-size: 0rpx;
    letter-spacing: -1rpx;
    font-family: Helvetica, "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 34rpx;
}
.text_1 {
    overflow-wrap: break-word;
    color: rgba(0,0,0,1.000000);
    font-size: 28rpx;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 34rpx;
}
.text_2 {
    overflow-wrap: break-word;
    color: rgba(0,0,0,1.000000);
    font-size: 28rpx;
    font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
    font-weight: normal;
    text-align: left;
    white-space: nowrap;
    line-height: 34rpx;
}
.thumbnail_1 {
    width: 34rpx;
    height: 22rpx;
    margin: 4rpx 0 8rpx 438rpx;
}
.thumbnail_2 {
    width: 30rpx;
    height: 22rpx;
    margin: 2rpx 0 10rpx 10rpx;
}
.image_1 {
    width: 50rpx;
    height: 24rpx;
    margin: 2rpx 0 8rpx 10rpx;
}
.group_3 {
    margin-top: 34rpx;
    flex-direction: row;
    display: flex;
}
.image_2 {
    width: 176rpx;
    height: 64rpx;
}
.text_3 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: right;
    white-space: nowrap;
    line-height: 44rpx;
    margin: 10rpx 0 0 120rpx;
}
.image_3 {
    width: 176rpx;
    height: 64rpx;
    margin-left: 108rpx;
}
.group_4 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: 16rpx;
    width: 702rpx;
    align-self: center;
    margin-top: 24rpx;
    display: flex;
    flex-direction: column;
    padding: 92rpx 112rpx 84rpx 110rpx;
}
.image-text_1 {
    width: 128rpx;
    align-self: center;
    display: flex;
    flex-direction: column;
}
.image_4 {
    width: 128rpx;
    height: 128rpx;
}
.text-group_1 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Semibold;
    font-weight: 600;
    text-align: left;
    white-space: nowrap;
    line-height: 44rpx;
    margin-top: 28rpx;
}
.text-wrapper_2 {
    width: 480rpx;
    height: 80rpx;
    background: #1750DC14;
    padding: 20rpx 184rpx 20rpx 184rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 80rpx;
}
.text_4 {
    overflow-wrap: break-word;
    color: rgba(23,80,220,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
}
.text-wrapper_3 {
    width: 480rpx;
    height: 80rpx;
    background: #1750DC;
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;
    padding: 20rpx 184rpx 20rpx 184rpx;
    border-radius: 40rpx;
    align-items: center;
    justify-content: center;
}
.text_5 {
    overflow-wrap: break-word;
    color: rgba(255,255,255,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
}
.group_5 {
    background-color: rgba(255,255,255,1.000000);
    border-radius: 16rpx;
    width: 702rpx;
    align-self: center;
    margin-top: 24rpx;
    display: flex;
    flex-direction: column;
    padding: 80rpx 24rpx 120rpx 24rpx;
}
.text_6 {
    overflow-wrap: break-word;
    color: rgba(51,51,51,1);
    font-size: 32rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    align-self: center;
}
.text_7 {
    width: 654rpx;
    height: 80rpx;
    overflow-wrap: break-word;
    color: rgba(153,153,153,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    line-height: 40rpx;
    margin-top: 32rpx;
}
.image-text_2 {
    width: 200rpx;
    align-self: center;
    margin-top: 40rpx;
    display: flex;
    flex-direction: column;
}
.group_6 {
    background-color: rgba(216,216,216,1.000000);
    width: 200rpx;
    height: 200rpx;
    display: flex;
    flex-direction: column;
}
.text-group_2 {
    overflow-wrap: break-word;
    color: rgba(102,102,102,1);
    font-size: 28rpx;
    font-family: PingFangSC-Regular;
    font-weight: normal;
    text-align: right;
    white-space: nowrap;
    line-height: 40rpx;
    align-self: center;
    margin-top: 24rpx;
}