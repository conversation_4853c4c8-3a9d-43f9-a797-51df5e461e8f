// pages/sportReports/index.js
const api = require("../../config/api");
const util = require("../../utils/util");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inquirerId:''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          let obj ={
            inquirerId:this.data.inquirerId
          }
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login?params=' + encodeURIComponent(JSON.stringify(obj))
          })
        }else{
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    }else{
      this.loadPage()
    }
  },

  loadPage(){
    const url = api.WebViewUrl + `/#/sportReports?inquirerId=${this.data.inquirerId}&token=` + wx.getStorageSync('token')
    // const url = 'https://localhost:8066' + `/#/sportReports?inquirerId=${this.data.inquirerId}&token=` + wx.getStorageSync('token')
    console.log(url)
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})