// pages/from/fromDetail/index.js
import {MgViewUrl} from "../../config/api-prod";

const api = require('../../config/api.js')
const util = require('../../utils/util.js')
import Dialog from '../../lib/vant-weapp/dialog/dialog'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    static: {
      meigao_normal: api.ImgUrl + 'images/meigao/meigao_normal.png',
      meigao_qsn_card: api.ImgUrl + 'images/meigao/meigao_qsn_card.png',
      meigao_jv_card: api.ImgUrl + 'images/meigao/meigao_jv_card.png',
      meigao_sm_card: api.ImgUrl + 'images/meigao/meigao_sm_card.png',
      meigao_yy_card: api.ImgUrl + 'images/meigao/meigao_yy_card.png',
    },
    imgPath:'',
    navTitle: '订单详情',
    orderId: '',
    mc: '',
    orderInfo: '',
    userInfo: '',
    pay: false,
    inquirerId: '',
    statusText: ['未支付', '已支付', '已取消', '已退款'],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.data.orderId = options.orderId
    this.data.pay = options.pay
    if (this.data.pay) {
      this.toPay()
    }
  },

  async toPay() {
    const res = await util.request(api.getPayInfo + '/' + this.data.orderId, {
    }, 'get')
    if (res.data.code === 100) {
      console.log(res.data.code, 'res.data.code')
      Dialog.confirm({
        message: res.data.msg
      })
      return
    }
    wx.requestPayment({
      ...res.data.data,
      success: () => {
        wx.navigateTo({
          url: `pages/mgPayDone/index?orderId=${this.data.orderId}`
        })
      },
      fail: () => {
      }
    })
  },
  buildQueryString(params) {
    const pairs = [];
    for (const [key, value] of Object.entries(params)) {
      pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    }
    return pairs.join('&');
  },

  // 发起诊前咨询
  async openCounselorChat() {
      wx.navigateTo({
        url: '/pages/consult/counselor/index?id=' + this.data.orderInfo.counselorId
      })
  },

  toMG() {
    // /ap/mg/product/info
    if (this.data.userInfo.inquirerId.toString() !== this.data.orderInfo.inquirerId.toString()) {
      Dialog.confirm({
        message: '选择的就诊人与订单绑定就诊人不匹配,请重新选择.'
      })
    } else {
      util.request(api.mgInfo, { inquirerId: this.data.orderInfo.inquirerId }, 'get')
        .then(mgInfo => {
          let url = `${MgViewUrl}?top=0&bottom=0&pt=${mgInfo.data.data.partnerToken}&pid=${mgInfo.data.data.partnerId}&from=mini&mc=${this.data.mc}`
          const params = encodeURIComponent(JSON.stringify(url))
          wx.navigateTo({
            url: `/pages/webView/index?url=${params}`
          })
        })
    }
  },

  initData() {
    util.request(api.mgOrderDetail + '/' + this.data.orderId, {
    }, 'get').then(res => {
      if (res.data.code === 0) {
        this.data.orderInfo = res.data.data
        switch (this.data.orderInfo.productId) {
          case 1003: //青少年
            this.data.mc = 'depression-teenager'
            this.data.imgPath = this.data.static.meigao_qsn_card
            break
          case 1002: //失眠
            this.data.mc = 'insomnia'
            this.data.imgPath = this.data.static.meigao_sm_card
            break
          case 1005: //抑郁
            this.data.mc = 'depression'
            this.data.imgPath = this.data.static.meigao_yy_card
            break
          case 1004: //焦虑
            this.data.mc = 'anxiety'
            this.data.imgPath = this.data.static.meigao_jv_card
            break
          case 1001:
            this.data.mc = 'daily'
            this.data.imgPath = this.data.static.meigao_normal
            break
          default:
            this.data.mc = ''
        }
        util.request(api.peopleList, {}, 'post').then(userInfo => {
          if (userInfo.data.code === 0) {
            this.data.userInfo = userInfo.data.data.find(item => {
              if (this.data.inquirerId) {
                return item.inquirerId.toString() === this.data.inquirerId.toString()
              } else {
                return item.inquirerId.toString() === this.data.orderInfo.inquirerId.toString()
              }
            })

            this.setData({
              orderId: this.data.orderId,
              userInfo: this.data.userInfo,
              orderInfo: this.data.orderInfo,
              imgPath: this.data.imgPath
            })
          }
        })
      }
    })
  },

  cancelOrder() {
    Dialog.confirm({
      message: '确定要取消该订单吗？'
    }).then(() => {
      util.request(api.mgCancelOrder + '?orderId=' + this.data.orderInfo.id, {}, 'post').then(res => {
        console.log(res)
        if (res.data.code === 0) {
          this.initData()
        } else {
          Dialog.confirm({
            message: res.data.msg
          })
        }
      })
    })
  },

  reOrder() {
    wx.navigateTo({
      url: '/pages/mgOrder/index?id=' + this.data.orderInfo.productId
    })
  },

  selectUser() {
    wx.navigateTo({
      url: '/pages/peopleContent/people/people?from=MEIGAO'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.initData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
