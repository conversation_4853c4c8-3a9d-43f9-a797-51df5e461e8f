<view class="page">
    <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <view class="block_2">
        <view class="group_3">
            <view class="group_4">
                <text lines="1" class="text_3">{{userInfo.name}}</text>
                <view class="text-wrapper_1">
                    <text lines="1" class="text_4">{{ userInfo.gender === 1?'男':'女' }}</text>
                    <text lines="1" class="text_5">{{ userInfo.age }}</text>
                </view>
            </view>
            <view class="group_5">
                <view class="text-wrapper_2">
                    <text lines="1" class="text_6">{{ userInfo.relationName }}</text>
                </view>
                <text lines="1" class="text_7">{{ userInfo.phone }}</text>
            </view>
            <view class="image-text_1" bind:tap="selectUser">
                <text lines="1" class="text-group_1">切换就诊人</text>
                <image src="/static/images/icon_arrow.png" class="thumbnail_3"></image>
            </view>
        </view>
        <view class="group_6">
            <view class="text-wrapper_4_1">
                <text lines="1" class="text_10_1">商品信息</text>
                <text lines="1" class="text_11_1">{{statusText[orderInfo.payStatus]}}</text>
            </view>
            <view class="section_1">
                <view class="image-text_2">
                    <image src="{{imgPath}}" class="image_3"></image>
                    <view class="text-group_2">
                        <text lines="1" class="text_9">{{orderInfo.productName}}</text>
                        <text lines="1" class="text_10">￥{{ orderInfo.amount }}</text>
                    </view>
                </view>
                <view class="text-wrapper_4">
                    <text lines="1" class="text_11">{{ orderInfo.delayPacketName||'全阶段' }}</text>
                </view>
            </view>
        </view>
        <view class="group_7">
            <view class="list_1">
                <view class="text-wrapper_5-0">
                    <text lines="1" class="text_12-0">订单编号</text>
                    <text lines="1" class="text_13-0">下单时间</text>
                </view>
                <view class="text-wrapper_5-1">
                    <text lines="1" class="text_12-1">{{ orderInfo.id }}</text>
                    <text lines="1" decode="true" class="text_13-1">{{ orderInfo.createdAt }}</text>
                </view>
            </view>
            <view class="text-wrapper_6">
                <text lines="1" class="text_14">订单金额</text>
                <text lines="1" class="text_15">￥{{ orderInfo.amount }}</text>
            </view>
            <view class="text-wrapper_7">
                <text lines="1" class="text_16">有效期</text>
                <text lines="1" class="text_17">{{ orderInfo.validDays || '--' }}天</text>
            </view>
        </view>
        <view wx-if="{{orderInfo.payStatus === 1 && orderInfo.counselorId}}" class="text-group_3">
            <text lines="1" class="text_18">售后咨询</text>
            <text lines="1" class="text_19">如您有数字疗法相关产品使用和内容设置等问题，可识别下方二维码联系心理健康管理师</text>
            <view>
                <view open-type='navigate'
                      hover-class="none"
                      class="item bg-color-white m20 p20">
                    <view class="info flex">
                        <view class="photo">
                            <image src="/static/images/counselor_default_avatar.png" mode="aspectFill"
                                   class="imgBlock"></image>
                        </view>
                        <view class="ml20 flex1">
                            <view class="name f32 b c333">{{orderInfo.counselorName}}<text class="ml20 n c666 f28">{{orderInfo.positionName ? orderInfo.positionName :'' }}</text>
                            </view>
                            <view class="text c666" wx:if="{{orderInfo.personalProfile}}">
                                <view>擅长：{{orderInfo.personalProfile ? orderInfo.personalProfile : '-'}}</view>
                            </view>
                            <view class="btn flex_m">
                                <view class="flex_c_m" catchtap="openCounselorChat" data-name='{{orderInfo.counselorName}}' data-id="{{orderInfo.counselorId}}">
                                    <view class="c666">
                                        免费咨询
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
    <view class="block_3">
            <view  wx:if="{{orderInfo.payStatus === 1}}" class="text_20" bind:tap="toMG">进入疗程</view>
            <view wx:if="{{orderInfo.payStatus === 2}}" class="text_20" bind:tap="reOrder">再次购买</view>
            <view wx:if="{{orderInfo.payStatus === 3}}" class="text_20" bind:tap="reOrder">再次购买</view>
            <view  wx:if="{{orderInfo.payStatus === 0}}" class="text_20_1" bind:tap="cancelOrder">取消订单</view>
            <view wx:if="{{orderInfo.payStatus === 0}}" class="text_20_2" bind:tap="toPay">去支付</view>
    </view>
</view>
<van-dialog id="van-dialog" confirm-button-color="#367DFF" />