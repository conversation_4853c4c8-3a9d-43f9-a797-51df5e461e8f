<view class="page-warp">
    <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <view class="content">
        <view class="item" wx:for="{{list}}" wx:key="index" data-id='{{item.id}}' bindtap="goDetail">
            <view class="item-text">{{item.title}}</view>
            <view class="item-time">{{item.releaseTime}}</view>
        </view>
    </view>
    <nodata text="{{'暂无数据'}}" wx:if="{{list.length === 0}}"></nodata>
    <van-toast id="van-toast" />
</view>