var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '戒烟限酒',
    list: [],
    artListQuery: {
      page: 1, // 页码
      type: 3,
    },
    artHasNext: false
  },

  //获取资讯内容
  async getartList() {
    util.showLoading({
      title: '加载中...',
      mask: true
    })
    try {
      const { artListQuery } = this.data
      const { data } = await util.request(
        api.articleList,
        {
          ...artListQuery
        },
        'GET',
        1,
        false
      )
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        list: artListQuery.page > 1 ? this.data.artList.concat(data.data.result) : data.data.result,
        artHasNext: data.data.hasNext
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },

  goDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/article/articleDetail/index?id=${id}&type=article`
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getartList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({
      ['artListQuery.page']: 1,
      ['artListQuery.type']: 3
    }, () => {
      this.getartList()
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (!this.data.artHasNext) {
      return
    }
    this.data.artListQuery.page++
    this.getartList()
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})