/* pages/caseList/index.wxss */
.select-warp {
  display: flex;
  align-items: center;
  background: #ffffff;
  width: 440rpx;
  height: 68rpx;
  box-shadow: 0rpx 0rpx 4rpx 0rpx rgba(221, 221, 221, 0.5);
  border-radius: 8rpx;
  margin: 28rpx 20rpx;
}

.select-people {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx;
  color: #999999;
  margin: auto 40rpx;
  white-space: nowrap;
}

.select-line {
  width: 2rpx;
  height: 28rpx;
  box-shadow: 0rpx 0rpx 4rpx 0rpx rgba(221, 221, 221, 0.5);
  border: 2rpx solid #dddddd;
  margin-right: 40rpx;
}

.van-dropdown-menu {
  height: auto !important;
}

.dropdown {
  width: 440rpx;
}

.item {
  border-radius: 20rpx;
  padding: 20rpx;
  position: relative;
}

.mb8 {
  margin-bottom: 8rpx;
}

.label {
  position: absolute;
  right: 0;
  top: 0;
  width: 96rpx;
  height: 44rpx;
  background: #ecf3ff;
  border-radius: 0rpx 8rpx 0rpx 8rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #367dff;
  text-align: center;
  line-height: 44rpx;
}

.noData {
  width: 100%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.imgEmpty {
  width: 420rpx;
  height: 312rpx;
}

.mt40 {
  margin-top: 40rpx;
}

.van-ellipsis {
  width: 170rpx;
  overflow-x: hidden;
  text-emphasis: ellipsis;
}

.my-dropdown-title {
  font-weight: 500 !important;
  font-size: 28rpx !important;
  color: #333 !important;
  padding: 0 !important;
  padding-right: 20rpx !important;
}
