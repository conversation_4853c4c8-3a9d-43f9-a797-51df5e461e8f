var api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({
  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '智能导诊',
    searchIcon: api.ImgUrl + 'images/adys/ic_input_search.png',
    bgImage: api.ImgUrl + 'images/adys/bg_img.png',
    value: '',
    relevantList: [],
    scrollViewHeight: 0,
    showList: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getDom()
  },
  
  getDom() {
    const query = this.createSelectorQuery()
    query.select('#searchWrap').boundingClientRect()
    query.exec(res => {
      const data = res[0]
      this.setData({
        scrollViewHeight: app.globalData.screenHeight - (data.top + data.height + app.globalData.navBarHeight)
      })
    })
  },

  // 搜索框输入时触发
  onChange(event) {
    if (!event.detail) {
      this.setData({
        relevantList: [],
        showList: false
      })
      return
    }
    this.setData({
      showList: true,
      value: event.detail
    })
    this.search(event.detail)
  },
  search(symptom) {
    util.request(api.symptomSearchList, {
      symptom,
      page: 1,
      offset: 1000
    }, 'get').then(res => {
      if (res.data.code === 0) {
        const list = res.data.data.result
        if (list && list.length > 0) {
          list.forEach((item) => {
            item.associateWord = item.symptomAlias ? item.symptom + '(' + item.symptomAlias + ')' : item.symptom 
          })
        }
        this.setData({
          relevantList: list,
          // showList: list && list.length > 0 ? true : false
        })
      } else {
        wx.showToast({
          title: res.data.msg,
          icon: 'none'
        })
      }
    })
  },

  handleClick(event) {
    const id = event.currentTarget.dataset.item.id
    wx.navigateTo({
      url: `/pages/guidanceDetail/index?id=${id}&keywords=${this.data.value}`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {},

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})