.container {
    background-color: #f9fafc;
  }
  
  .guidance-title {
    margin: 112rpx auto;
    text-align: center;
  }
  
  .t1 {
    font-weight: 600;
    font-size: 68rpx;
    color: #002f85;
  }
  
  .t2 {
    font-size: 28rpx;
    color: #002f85;
    font-weight: 400;
    margin-top: 15rpx;
  }
  
  .search-box {
    height: 89rpx;
    background: #ffffff;
    border-radius: 46rpx;
    border: 2rpx solid #d8dde5;
    margin: 80rpx 40rpx 0;
  }
  
  .search-input {
    display: flex;
    align-items: center;
    height: 100%;
  }
  
  .search-icon {
    width: 44rpx;
    height: 44rpx;
    margin-left: 40rpx;
    margin-right: 20rpx;
  }
  
  .search-input input {
    width: calc(100% - 50rpx);
    height: 100%;
  }
  
  .van-field__input {
    font-size: 30rpx;
  }
  
  .search-input-field {
    width: 85%;
  }
  
  .van-cell {
    border-radius: 46rpx;
    padding: 0 20rpx 0 0 !important;
  }
  
  .relevant {
    position: fixed;
    left: 0;
    width: 100%;
    padding: 0 40rpx 40rpx 40rpx;
    background: #f9fafc;
    z-index: 100;
    box-sizing: border-box;
    overflow: scroll;
  }
  
  .relevant-item {
    height: 100rpx;
    display: flex;
    align-items: center;
    border-bottom: 0.5rpx solid #eee;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
  }
  
  .search-icon-item {
    width: 44rpx;
    height: 44rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
  }
  
  .bg-image {
    display: flex;
    justify-content: center;
    margin-top: 148rpx;
  }  