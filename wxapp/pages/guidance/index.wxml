<view class="container">
    <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
    <view class="guidance-title">
        <view class="t1">智能导诊</view>
        <view class="t2">描述疾病 推荐科室</view>
    </view>
    <!-- 搜索区域 -->
    <view class="search-box" id="searchWrap">
        <view class="search-input">
            <image class="search-icon" src="{{searchIcon}}" mode="scaleToFill" />
            <van-field
                class="search-input-field"
                value="{{ value }}"
                clearable
                placeholder="搜索疾病"
                border="{{ false }}"
                bind:change="onChange"
            />
        </view>
    </view>
    <view wx:if="{{showList}}" class="relevant" style="height: {{scrollViewHeight}}px">
        <view class="relevant-item" wx:for="{{relevantList}}" data-item="{{item}}" bind:tap="handleClick">
            <image class="search-icon-item" src="{{searchIcon}}"></image>
            <text>{{ item.associateWord }}</text>
        </view>
    </view>
    <view class="bg-image">
        <image src="{{bgImage}}" mode="widthFix" />
    </view>
</view>