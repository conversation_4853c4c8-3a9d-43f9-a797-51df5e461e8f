// pages/from/fromDetail/index.js
import api from "../../../config/api";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    id: '',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
     this.data.id = options.id
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    let url =api.WebViewUrl + `/#/formRes?id=${this.data.id}&token=` + wx.getStorageSync('token')
    
    // let url ='https://localhost:8066' + `/#/formRes?id=${this.data.id}&token=` + wx.getStorageSync('token')
    console.log(url)
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
