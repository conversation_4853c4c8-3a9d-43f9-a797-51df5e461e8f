page {
  background-color: #f7f7f7;
}
.page-evaluation-result {
  height: calc(100vh - var(--status-bar-height));
  padding: 24rpx;
}
.content {
  width: 702rpx;
  height: 788rpx;
  background: #1750DC;
  border-radius: 16rpx;
}
.content-top {
  padding: 32rpx;
}
.title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 44rpx;
}
.subtitle {
  margin-top: 8rpx;
  margin-bottom: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 40rpx;
}
.result {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: center; */
  width: 702rpx;
  height: 638rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
}
.img {
  margin-top: 70rpx;
  width: 188rpx;
  height: 188rpx;
}
.score{
  position: absolute;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  margin-top: 134rpx;
  font-size: 48rpx;
  color: #FFFFFF;
  text-shadow: 0px 0px 8px rgba(0,176,67,0.6);
  font-style: normal;
}
.result-type {
  margin-top: 28rpx;
  margin-bottom: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 36rpx;
  color: #333333;
  line-height: 50rpx;
}
.textarea {
  padding: 24rpx;
  width: 574rpx;
  height: 220rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
  background: #F5F5F5 !important;
}
.footer {
  position: fixed;
  display: flex;
  justify-content: center;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #fff;
  height: 160rpx;
  padding: 16rpx 8rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 686rpx;
  height: 80rpx;
  background: #1750DC;
  color: #fff;
  border-radius: 80rpx;
}
