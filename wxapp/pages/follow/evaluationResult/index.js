//index.js
var api = require('../../../config/api.js')
const util = require("../../../utils/util");
// var util = require('../../../utils/util')
// const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    static: {
      ic_follow_visit: api.ImgUrl + 'images/ic_follow_visit.png',
      ic_follow_questionnaire: api.ImgUrl + 'images/ic_follow_questionnaire.png',
      img_blank_noprescription: api.ImgUrl + 'images/img_blank_noprescription.png'
    },
    id:'',
    orderId: '',
    followUpFormId: '',
    form:{}
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.orderId = options.orderId

    util.request(api.formReport + '/' + this.data.orderId, {}, 'get').then(res=>{
      this.data.form = res.data.data
      this.data.id = res.data.data.followUpFormId
      this.setData({
        form:this.data.form
      })
    })
  },

  retryEvaluation() {
    console.log('retryEvaluation')
    console.log(this.data.id)
    wx.redirectTo({
      url: `/pages/follow/answerQuestion/index?id=${this.data.id}`
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
