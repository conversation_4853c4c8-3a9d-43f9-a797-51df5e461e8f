//index.js
var api = require('../../../config/api.js')
var util = require('../../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    content: {},
    static: {
      ic_form_black: api.ImgUrl + 'images/ic_form_black.png'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      id: options.id
    })
  },
  async getData() {
    try {
      const { id } = this.data
      const { data } = await util.request(api.getFollowDetail, { id }, 'get')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        content: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  handleGohistory() {
    console.log(this.data.content.consultType)
    app.globalData.doctorName = ''
    app.globalData.consultType = this.data.content.consultType
    wx.reLaunch({
      url: '/pages/consult/index/index'
    })
  },
  async handleGoFrom(e) {
    const {
      id,
      formid
    } = e.currentTarget.dataset
    try {
      const { data } = await util.request(`${api.getFollowupStatus}?id=${id}`)
      if (data.data.status === 0 || data.data.status === 1) {
        util.showToast({
          title: '当前计划暂未开始'
        })
        return
      }
      if (data.data.status === 3) {
        util.showToast({
          title: '当前计划已取消'
        })
        return
      }
      const { followUpFormList = [] } = this.data.content || {}
      const fromData = followUpFormList.find(({ followUpRecordFormId }) => followUpRecordFormId == formid) || {}
      wx.navigateTo({
        url: `/pages/from/fromDetail/index?id=${id}&formId=${formid}&finishStatus=${fromData.fillingStatus}`
      })
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getData()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
