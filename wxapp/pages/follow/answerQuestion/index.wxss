page {
  background-color: #f7f7f7;
}
.page-answer-queation {
  height: calc(100vh - var(--status-bar-height));
  padding: 24rpx;
  overflow-y: scroll;
}
.question-container {
  position: relative;
  padding: 38rpx 32rpx;
  width: 702rpx;
  height: auto;
  background: #FFFFFF;
  border-radius: 16rpx;
}
.progress {
  margin-bottom: 46rpx;
  padding: 0  16rpx;
  width: 522rpx;
}
.count {
  position: absolute;
  top: 24rpx;
  right: 48rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
}
.question {
  max-width: 520rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
checkbox .wx-checkbox-input {
  width: 32rpx;
  height: 32rpx;
}

.answer-text {
  max-width: 520rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 30rpx;
  color: #333333;
  line-height: 42rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.radio {
  margin-top: 32rpx;
}
.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 16rpx 8rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 686rpx;
  height: 80rpx;
  background: #1750DC;
  color: #fff;
  border-radius: 80rpx;
}
.common {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 320rpx;
  height: 80rpx;
  background: #1750DC;
  color: #fff;
  font-family: AppleSystemUIFont;
  line-height: 40rpx;
  font-size: 34rpx;
  border-radius: 80rpx;
}
.double-btn {
  display: flex;
  justify-content: center;
}
.prev-btn {
  margin-right: 46rpx;
  background-color: #e7edfb;
  color: #1750DC;
}

page {
  position: relative;
  background: #F7F7F7;
}
.page_content {
  height: calc(100vh - var(--status-bar-height));
  padding: 24rpx;
}
.content {
  display: flex;
  flex-direction: column;
  padding: 40rpx 24rpx;
  width: 702rpx;
  height: 182rpx;
  background-image: url("https://patient-pro.naiterui.com/images/img_special_subject.png");
  border-radius: 16rpx;
}
.title {
  display: flex;
  align-items: center;
}
.title-text {
  margin-right: 24rpx;
  margin-bottom: 16rpx;
  max-width: 446rpx;
  height: 46rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.total {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
}
.desc {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}
.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 16rpx 8rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 686rpx;
  height: 80rpx;
  background: #1750DC;
  color: #fff;
  border-radius: 80rpx;
}
