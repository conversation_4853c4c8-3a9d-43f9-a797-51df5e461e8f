//index.js
var api = require('../../../config/api.js')
const util = require("../../../utils/util");
// var util = require('../../../utils/util')
// const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const { _o } = util.headerParams
    this.data.id = options.id
    const url = api.WebViewUrl + `/#/formDetail?id=${this.data.id}&token=` + wx.getStorageSync('token')
    // const url = 'https://localhost:8066' + `/#/formDetail?&origin=${_o}&id=${this.data.id}&token=` + wx.getStorageSync('token')
    console.log(url)
    this.setData({
      url,
    })

  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {

  }
})
