//index.js
var api = require('../../../config/api.js')
const util = require("../../../utils/util");
// var util = require('../../../utils/util')
// const app = getApp()
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    static: {
      ic_follow_visit: api.ImgUrl + 'images/ic_follow_visit.png',
      ic_follow_questionnaire: api.ImgUrl + 'images/ic_follow_questionnaire.png',
      img_form_icon: api.ImgUrl + 'images/img_form_icon.png',
      img_blank_noprescription: api.ImgUrl + 'images/img_blank_noprescription.png'
    },
    params:{
      num: 10,
      page: 1
    },
    active: 0,
    list: [],
    myList:[]
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad() {
    this.getListFirst()
  },

  getListFirst(){
    util.request(api.formList, { ...this.data.params }, 'get').then(res=>{
      if (res.data.code === 0){{
        this.data.hasNext = res.data.data.hasNext
        if (this.data.params.page > 1){
          this.data.list = this.data.list.concat(res.data.data.result)
        }else {
          this.data.list = res.data.data.result
        }
        this.setData({
          list:this.data.list,
        })
        console.log(this.data.list)
      }}
    })
  },
  getListSe(){
    util.request(api.reportList, { ...this.data.params }, 'get').then(res=>{
      if (res.data.code === 0){{
        this.data.hasNext = res.data.data.hasNext
        if (this.data.params.page > 1){
          this.data.myList = this.data.myList.concat(res.data.data.result)
        }else {
          this.data.myList = res.data.data.result
        }
        this.setData({
          myList:this.data.myList,
        })
      }}
    })
  },

  onChange() {
    this.data.params.page = 1
    const newActive = this.data.active === 0 ? 1 : 0
    this.setData({
      active: newActive
    })
    if (newActive === 0){
      this.getListFirst()
    }else {
      this.getListSe()
    }
  },

  goToCurrentQuePage(e) {
    console.log(e)
    const {
      id
    } = e.currentTarget.dataset
    const item = this.data.list.find(r=>{
      return id == r.id
    })
    wx.navigateTo({
      url: `/pages/follow/evaluation/index?id=${id}&active=1&name=${item.name}&questionNum=${item.questionNum}&description=${item.description || ''}`
    })
  },

  goToCurrentEvaluationResult(e) {
    const {
      id
    } = e.currentTarget.dataset
    console.log(id)
    wx.navigateTo({
      url: `/pages/follow/resultHfive/index?id=${id}`
    })
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  onScrollToLower(){
    if (this.data.hasNext){
      this.data.params.page += 1
      if (this.data.active === 0){
        this.getListFirst()
      }else {
        this.getListSe()
      }
    }
  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow() {

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage: function() {

  }
})
