<navbar backgroundColor='#fff' navTitle="量表评测"></navbar>
  <van-tabs active="{{ active }}" color="#367DFF" title-active-color="#1750DC" line-width="90" sticky bind:change="onChange">
    <van-tab title="量表评测"></van-tab>
    <van-tab title="我的评测"></van-tab>
  </van-tabs>
<view class="list-container">
  <view class="list" wx:if='{{active===0}}'>
    <scroll-view bindscrolltolower="onScrollToLower" scroll-y="true" class="list" wx:if='{{list.length > 0}}'>
      <view class="single-list" wx:for="{{list}}" wx:key="id" data-id="{{item.id}}" bind:tap="goToCurrentQuePage">
        <view class="image">
          <image class="no_msg" src="{{static.img_form_icon}}"></image>
        </view>
        <view class="left">
          <view class="left_top ellipsis">{{item.name}}</view>
          <view wx-if="{{item.description}}" class="left_top ellipsis top_item">{{item.description || ''}}</view>
          <view class="left_bottom top_item">共{{ item.questionNum }}题</view>
        </view>
      </view>
    </scroll-view>
    <view class="flex_line_c flex1 empty_icon" wx:else>
      <image class="no_msg" src="{{static.img_blank_noprescription}}"></image>
      <view class="f28 c666">当前暂无量表评测</view>
    </view>
  </view>
  <view wx:else>
    <scroll-view bindscrolltolower="onScrollToLower" scroll-y="true" class="list" wx:if='{{list.length}}'>
      <view class="my-single-list" wx:for="{{myList}}" wx:key="id" data-id="{{item.id}}" bind:tap="goToCurrentEvaluationResult">
        <view class="title">{{item.name}}</view>
        <view class="result">
          <view class="text">测评结果：</view>
          <view class="score">{{item.totalScore}}</view>
          <view class="type">{{item.result}}</view>
        </view>
        <view class="time">{{item.submitAt}}</view>
      </view>
    </scroll-view>
    <view class="flex_line_c flex1 empty_icon" wx:else>
      <image class="no_msg" src="{{static.img_blank_noprescription}}"></image>
      <view class="f28 c666">当前暂无我的评测</view>
    </view>
  </view>
</view>