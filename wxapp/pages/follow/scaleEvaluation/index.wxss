page {
  background: #f8f8f8;
}
.list-container {
  padding: 24rpx;
}
.empty_icon {
  margin-top: 50%;
}
.list {
  height: calc(100vh - 264rpx - 48rpx);
  overflow-y: scroll;
}
.single-list {
  display: flex;
  margin-top: 24rpx;
  padding: 24rpx;
  width: 702rpx;
  height: 180rpx;
  background-color: #fff;
  border-radius: 32rpx;
}
.image {
  margin-right: 16rpx;
  width: 132rpx;
  height: 132rpx;
  border-radius: 8rpx;
}
.image image{
  width: 132rpx;
  height: 132rpx;
}
.no_msg {
  width: 158rpx;
  height: 158rpx;
  border-radius: 8rpx;

}
.left {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 16rpx;
}

.left_top {
  width: 480rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 32rpx;
}

.top_item{
  color: #666666;
  font-size:28rpx;
  line-height: 28rpx;
  font-weight: normal
}

.ellipsis {
  white-space: nowrap;      /* 禁止换行 */
  overflow: hidden;        /* 隐藏超出部分 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.left_bottom {
  height: 40rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #1750DC;
  background: #F0F0F0;
  width: fit-content;
  padding: 0 12rpx;
  line-height: 40rpx;
  border-radius: 6rpx;
}
.my-single-list {
  width: 702rpx;
  height: 214rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  margin-top: 24rpx;
  padding: 24rpx;
}
.title {
  max-width: 652rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.result {
  margin-top: 16rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
}
.text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}
.score {
  margin-right: 24rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28rpx;
  color: #00DE55;
  line-height: 40rpx;
}
.type {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
}
.time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 40rpx;
}