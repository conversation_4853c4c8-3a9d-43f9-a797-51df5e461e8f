//index.js
var api = require('../../../config/api.js')
// var util = require('../../../utils/util')
// const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    static: {
      ic_follow_visit: api.ImgUrl + 'images/ic_follow_visit.png',
      ic_follow_questionnaire: api.ImgUrl + 'images/ic_follow_questionnaire.png',
      img_blank_noprescription: api.ImgUrl + 'images/img_blank_noprescription.png'
    },
    id:'',
    questionNum:'',
    name:'',
    description:''
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.data.id = options.id
    this.data.name = options.name
    this.data.questionNum = options.questionNum
    this.data.description = options.description
    this.setData({
      id:this.data.id,
      questionNum:this.data.questionNum,
      name:this.data.name,
      description:this.data.description
    })
  },

  startEvaluation() {
    wx.navigateTo({
      url: `/pages/follow/answerQuestion/index?id=${this.data.id}`
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
