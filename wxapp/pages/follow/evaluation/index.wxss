page {
  position: relative;
  background: #F7F7F7;
}
.page_content {
  height: calc(100vh - var(--status-bar-height));
  padding: 24rpx;
}
.content {
  display: flex;
  flex-direction: column;
  padding: 40rpx 24rpx;
  width: 702rpx;
  background-image: url("https://patient-pro.naiterui.com/images/img_special_subject.png");
  border-radius: 16rpx;
}
.title {
  display: flex;
  align-items: baseline;
}
.title-text {
  margin-right: 24rpx;
  margin-bottom: 16rpx;
  max-width: 446rpx;
  height: 46rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.total {
  height: 40rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #1750DC;
  background: #F0F0F0;
  width: fit-content;
  padding: 0 12rpx;
  line-height: 40rpx;
  border-radius: 6rpx;
}
.desc {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 40rpx;
}
.footer {
  display: flex;
  justify-content: center;
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #fff;
  height: 160rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 686rpx;
  height: 80rpx;
  background: #1750DC;
  margin-top: 16rpx;
  color: #fff;
  border-radius: 80rpx;
}
