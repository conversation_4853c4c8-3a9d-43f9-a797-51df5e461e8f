const api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    artList: [],
    navArr: [],
    artListQuery: {
      page: 1, // 页码
      type: 2,
      groupId: ''
    },
    mainActiveIndex: 0,
    artHasNext: false,
    items: [],
    noData: false,
    hasMore: true,
    classifyHeight: 0,
    targetGroupId: '' // 存储目标分组ID
  },

  /**
   *  获取新闻资讯组
   */
  async getList() {
    return new Promise(async (resolve, reject) => {
      try {
        const { data } = await util.request(api.groupsList, {}, 'get', 1, false)
        if (data.code !== 0) {
          throw new Error(data.msg)
        }
        data.data.forEach(item => {
          item.text = item.name
        })
        this.setData({
          items: data.data
        })
        resolve(data.data)
      } catch (error) {
        util.showToast({
          title: error.message,
          icon: 'none',
          duration: 3000
        })
        reject(error)
      }
    })
  },

  //获取资讯内容
  async getartList() {
    util.showLoading({
      title: '加载中...',
      mask: true
    })
    try {
      const { artListQuery } = this.data
      const { data } = await util.request(
        api.articleList,
        {
          ...artListQuery
        },
        'GET',
        1,
        false
      )
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      this.setData({
        artList: artListQuery.page > 1 ? this.data.artList.concat(data.data.result) : data.data.result,
        artHasNext: data.data.hasNext,
        noData: data.data.result.length === 0,
        hasMore: this.data.artListQuery.page < data.data.totalPages,
        'artListQuery.page': this.data.artListQuery.page + 1
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },

  // 点击二级分类
  onClickNav({ detail = {} }) {
    console.log(detail)
    this.setData(
      {
        mainActiveIndex: detail.index || 0,
        'artListQuery.groupId': detail.item.id,
        'artListQuery.page': 1,
        artList: []
      },
      () => {
        this.getartList()
      }
    )
  },

  // 处理滚动到底部加载更多
  handleReachBottom() {
    if (!this.data.hasMore) {
      return // 如果正在加载或没有更多数据，则不执行任何操作
    }
    this.getartList()
  },

  goDetail(e) {
    const { id } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/article/articleDetail/index?id=${id}&type=article`
    })
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options, 'options')

    // 处理扫码场景，解析scene参数
    if (options && options.scene) {
      try {
        // 解码scene参数
        const scene = decodeURIComponent(options.scene);
        console.log('Decoded scene:', scene);

        // 解析scene中的参数，格式可能是 groupId=X 或者就是 groupId 值
        let groupId = '';
        if (scene.includes('=')) {
          // 如果是 key=value 格式
          const params = scene.split('&');
          for (const param of params) {
            const [key, value] = param.split('=');
            if (key === 'groupId') {
              groupId = value;
              break;
            }
          }
        } else {
          // 如果scene就是groupId值
          groupId = scene;
        }

        if (groupId) {
          this.setData({
            targetGroupId: groupId
          });
          console.log('Set targetGroupId from scene:', groupId);
        }
      } catch (error) {
        console.error('Failed to decode scene parameter:', error);
      }
    }
    // 保存传入的分组ID参数（如果有）
    else if (options && options.groupId) {
      this.setData({
        targetGroupId: options.groupId
      })
    }

    // 加载分类列表
    this.getList().then(categories => {
      // 如果有目标分组ID，查找对应的分组并自动选择
      if (this.data.targetGroupId && categories && categories.length > 0) {
        this.selectGroupById(this.data.targetGroupId)
      }
    }).catch(err => {
      console.error('Failed to load categories:', err)
    })
  },

  /**
   * 根据分组ID查找并选择对应的分组
   */
  selectGroupById(groupId) {
    const { items } = this.data
    if (!items || items.length === 0) return

    // 查找匹配的分组索引
    // 将 groupId 转换为数字类型，确保与 item.id 类型一致
    const targetIndex = items.findIndex(item => item.id == groupId)

    // 如果找到匹配的分组
    if (targetIndex !== -1) {
      // 获取选中分组的ID，确保类型一致
      const selectedGroupId = items[targetIndex].id

      this.setData({
        mainActiveIndex: targetIndex,
        'artListQuery.groupId': selectedGroupId,
        'artListQuery.page': 1,
        artList: []
      }, () => {
        this.getartList()
      })
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    setTimeout(() => {
      // 检查 items 数组是否有数据
      if (this.data.items && this.data.items.length > 0) {
        // 如果有目标分组ID，尝试选择对应分组
        if (this.data.targetGroupId) {
          this.selectGroupById(this.data.targetGroupId)
          // 设置高度但不重置分组ID（已在selectGroupById中设置）
          this.setData({
            classifyHeight: app.globalData.screenHeight - app.globalData.navBarHeight - 100
          })
        } else {
          // 没有目标分组，使用默认的第一个分组
          this.setData({
            classifyHeight: app.globalData.screenHeight - app.globalData.navBarHeight - 100,
            'artListQuery.groupId': this.data.items[0].id
          }, () => {
            this.getartList()
          })
        }
      } else {
        // 如果 items 为空，先获取分类列表
        this.getList().then(() => {
          if (this.data.items && this.data.items.length > 0) {
            // 如果有目标分组ID，尝试选择对应分组
            if (this.data.targetGroupId) {
              this.selectGroupById(this.data.targetGroupId)
              // 设置高度但不重置分组ID（已在selectGroupById中设置）
              this.setData({
                classifyHeight: app.globalData.screenHeight - app.globalData.navBarHeight - 100
              })
            } else {
              // 没有目标分组，使用默认的第一个分组
              this.setData({
                classifyHeight: app.globalData.screenHeight - app.globalData.navBarHeight - 100,
                'artListQuery.groupId': this.data.items[0].id
              }, () => {
                this.getartList()
              })
            }
          }
        })
      }
    }, 1000)
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})