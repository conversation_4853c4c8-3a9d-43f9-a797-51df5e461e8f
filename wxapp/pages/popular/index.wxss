page {
  background: #fff;
}

/* 左侧选项样式类 */
.main-item-class {
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 28rpx !important;
  color: #525252 !important;
  padding: 30rpx 12rpx 30rpx 16rpx !important;
  /* margin-bottom: 10rpx; */
}

/* 左侧选项选中样式类 */
.main-active-class {
  font-weight: 500 !important;
  font-size: 28rpx !important;
  color: #367dff !important;
  /* border-color: #367dff!important; */
  border-left: none !important;
  margin-left: 3px !important;
  background: #ffffff;
  border-radius: 20rpx 0rpx 0rpx 20rpx;
}

.main-active-class::before {
  content: '';
  position: absolute;
  left: 0;
  height: 20px;
  width: 3px;
  border-radius: 5px;
  background-color: #367dff;
}

/* 左侧区域样式 */
.van-tree-select__nav {
  background-color: #f5f7fa !important;
  flex: none !important;
  width: 200rpx;
}

.van-search__content--round {
  border: 1rpx solid #dfdfdf;
  height: 72rpx;
  line-height: 72rpx;
}

/* .van-tree-select {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
} */

.good-wrap {
  margin: 20rpx 34rpx 20rpx 20rpx;
}

.wrap {
  display: flex;
  height: 149rpx;
  border-bottom: 1px solid #eee;
  margin-bottom: 20rpx;
}

.left {
  flex: 1;
  height: 128rpx;
  margin-right: 20rpx;
}

.left image {
  width: 100%!important;
  height: 100%!important;
  border-radius: 10rpx;
}

.right {
  display: flex;
  flex-direction: column;
  width: 308rpx;
  
}

.text {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  line-height: 40rpx;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 限制为两行 */
  text-overflow: ellipsis;
}

.time {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 34rpx;
  margin-top: 14rpx;
}

.load-more {
  text-align: center;
  margin: 20rpx auto;
  font-size: 26rpx;
  color: #333;
}
