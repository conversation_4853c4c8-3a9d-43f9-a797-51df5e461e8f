<view class="container">
  <van-tree-select
      items="{{ items }}"
      main-active-index="{{ mainActiveIndex }}"
      height="{{classifyHeight}}"
      main-item-class="main-item-class"
      main-active-class="main-active-class"
      bind:click-nav="onClickNav"
      bind:reach-bottom="handleReachBottom"
    >
    <view slot="content" class="good-wrap">
      <view class="wrap" wx:for="{{artList}}" wx:key="index" data-id='{{item.id}}' bindtap="goDetail">
        <view class="left">
          <image src="{{item.imgUrl}}" mode="widthFix"></image>
        </view>
        <view class="right">
          <view class="text">{{item.title}}</view>
          <view class="time">{{item.releaseTime}}</view>
        </view>
      </view>
      <view class="load-more">{{!hasMore && !noData ? '没有更多了' : ''}}</view>
      <nodata wx:if="{{noData}}"></nodata>
    </view>
  </van-tree-select>
</view>