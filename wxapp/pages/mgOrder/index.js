// pages/from/fromDetail/index.js
const api = require('../../config/api.js')
const util = require('../../utils/util.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inquirerId: '',
    id:'',
    url: '',
    type: '',

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options)
    this.data.id = options.id
    this.data.type = options.type
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.init()
  },

  async init() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          // 切换到登录页面
          let obj = {
            inquirerId: this.data.inquirerId,
            id:this.data.id,
            url: this.data.url,
            type: this.data.type,
          }
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login?params=' + encodeURIComponent(JSON.stringify(obj))
          })
        }else {
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    } else {
      this.loadPage()
    }
  },

  loadPage(){
    const url = api.WebViewUrl + `/#/dtxOrder?id=${this.data.id}&type=${this.data.type}&inquirerId=${this.data.inquirerId}&token=` + wx.getStorageSync('token')
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
