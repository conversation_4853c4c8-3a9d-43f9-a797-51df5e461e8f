page {
  background: #f8f8f8;
}

.page-section swiper {
  height: 640rpx;
}

.page-section swiper image {
  height: 640rpx;
}

.goods-name-wrap {
  background-color: #fff;
  margin-bottom: 20rpx;
  border-top: 1rpx solid #eee;
}

.goods-name {
  padding-top: 28rpx;
  font-size: 32rpx;
  margin-left: 28rpx;
  font-weight: 700;
}

.goods-name view {
  margin-right: 16rpx;
  display: inline-block;
}

.goods-name-wrap .price-warp {
  display: flex;
  margin-left: 28rpx;
  color: rgba(255, 0, 0, 1);
  padding-bottom: 20rpx;
  font-size: 34rpx;
}

.swiper-item-inner {
  position: relative;
}

.swiper {
  position: relative;
}

.swiper-item-num {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 40rpx;
  background: rgba(204, 204, 204, 1);
  border-radius: 4rpx;
  color: #fff;
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  text-align: center;
  font-size: 26rpx;
}

.price-warp .unit {
  margin-right: 10rpx;
}

.price-warp .price {
  font-weight: 500;
}

.goods-detail {
  height: 100%;
  background-color: #fff;
  padding: 24rpx 28rpx 0;
  box-sizing: border-box;
}

.goods-detail .goods-detail-title .goodsDetails {
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
}

.goodsDetails {
  background-color: #ffffff;
  padding: 10rpx 28rpx 168rpx;
  box-sizing: border-box;
}

.openLayout {
  display: flex;
  align-content: center;
  justify-content: center;
  background-color: #ffffff;
  padding: 10rpx 0;
}

.openStatus {
  font-size: 24rpx;
  color: #333333;
  text-align: center;
}

.arrow {
  width: 30rpx;
  height: 30rpx;
}

.goods-detail-content {
  border-top: 1rpx solid #eeeeee;
  margin-top: 16rpx;
}

.goods-detail-content .content-item {
  display: flex;
  border-bottom: 1rpx solid #eeeeee;
  /* padding: 20rpx 14rpx; */
}

.goods-detail-content .content-item .label {
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  width: 200rpx;
  border-left: 1rpx solid #eeeeee;
  border-right: 1rpx solid #eeeeee;
  padding: 20rpx 14rpx;
}

.goods-detail-content .content-item .content {
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 36rpx;
  flex: 1;
  border-right: 1rpx solid #eeeeee;
  padding: 20rpx 14rpx;
}

.footer {
  width: 100%;
  background: #ffffff;
  position: fixed;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  /* border-top: 1px solid #EEEEEE; */
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
}

.footer-content {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 98rpx;
  padding-left: 60rpx;
  padding-right: 20rpx;
}

.footer .left {
  font-size: 18rpx;
}

.footer .left .van-icon {
  font-size: 48rpx;
}

.footer .right {
  display: flex;
}

.footer .right .btn {
  width: 216rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #ff9b3a;
  border-radius: 38rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}

.footer .right .cart-btn {
  background: #ff9b3a;
  margin-right: 12rpx;
}

.footer .right .buy-btn {
  background: #367dff;
}

.footer .right .no-btn {
  background: #bbbbbb;
}

/* 弹框 */
.choose-sku {
  padding: 28rpx;
}

.choose-sku .top {
  display: flex;
}

.choose-sku .top image {
  width: 180rpx;
  height: auto;
  /* height: 180rpx; */
  background: #f8f8f8;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.choose-sku .top .detail {
  margin-right: 74rpx;
  /* display: flex; */
}

.choose-sku .top .price {
  color: #f05542;
  margin-top: 8rpx;
}

.choose-sku .top .detail-inner view {
  display: inline-block;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
  margin-right: 10rpx;
}

.choose-sku .center {
  margin-top: 60rpx;
}

.choose-sku .center .sku-warp {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
  margin-bottom: 50rpx;
}

.choose-sku .center .sku-item {
  /* width: 144rpx; */
  padding: 0 20rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  background: #f5f5f5;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.choose-sku .center .sku-item:last-child {
  margin-right: 0;
}

.choose-sku .center .sku-item-active {
  background: #ecf3ff;
  font-weight: 500;
  color: #367dff;
}

.choose-sku .bottom {
  display: flex;
  justify-content: space-between;
  margin-bottom: 70rpx;
}

.choose-sku .btn-warp .btn {
  height: 80rpx;
  line-height: 80rpx;
  background: #367dff;
  border-radius: 8rpx;
  width: 100%;
  text-align: center;
  color: #fff;
}

.section-title {
  font-size: 28rpx !important;
  font-weight: 700;
}

.cover {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background: rgba(255, 255, 255, 0.8);
  font-size: 30rpx;
  z-index: 999;
}

.layer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.custom-share-button {
  margin: 0;
  padding: 0;
  line-height: normal;
  display: flex;
  align-items: center;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
}

.share-icon {
  width: 24px;
  height: 24px;
}

.share-text {
  font-size: 20rpx;
  color: #333;
  margin-top: 5rpx;
}

.share-btn {
  padding-left: 54rpx;
  margin-top: 16rpx;
  z-index: 99;
}

.rich-img {
  width: 100%;
  margin: 20rpx 0;
}

/* mp-html styles */
.goodsDetails mp-html image {
  width: 100% !important;
  margin: 20rpx 0;
}
