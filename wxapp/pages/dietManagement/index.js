// pages/dietManagement/index.js
const api = require("../../config/api");
const util = require("../../utils/util");
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inquirerId: '',
    url: '',
    type: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    if (options.type)this.data.type = options.type
    this.data.inquirerId = options.inquirerId
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    const userInfo = wx.getStorageSync('userInfo')
    if (!userInfo.userId) {
      util.loginByWeixin().then(response=>{
        if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
          let obj = {
            inquirerId: this.data.inquirerId,
            type: this.data.type
          }
          // 切换到登录页面
          wx.navigateTo({
            url: '/pages/auth/login/login?params=' + encodeURIComponent(JSON.stringify(obj))
          })
        }else{
          wx.setStorageSync('userinfo', response.data.data.userInfo)
          this.loadPage()
        }
      })
    }else{
      this.loadPage()
    }
  },

  loadPage(){
    // let url ='https://localhost:8066/' + `#/dietManagement?token=` + wx.getStorageSync('token')
    let url =api.WebViewUrl + `#/dietManagement?token=` + wx.getStorageSync('token')
    if (this.data.type)url+= `&type=${this.data.type}`
    if (this.data.inquirerId)url+= `&inquirerId=${this.data.inquirerId}`
    console.log(url)
    this.setData({
      url,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})